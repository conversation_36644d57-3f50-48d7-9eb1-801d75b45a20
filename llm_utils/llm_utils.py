import asyncio
import csv
import json
import re
from datetime import date, datetime, timedelta, timezone
from enum import Enum
from io import StringIO
from typing import Any, List, Optional, Sequence, TypeVar, Union, get_args, get_origin

from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage
from pydantic import BaseModel
from rapidfuzz import fuzz, process, utils

from baml_client import b
from flight_agent.flights_tools import airline_iata_code_to_name, airline_name_to_iata_code
from server.database.models.user_profile import UserProfile
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.utils.logger import logger
from server.utils.s3_utils import s3_utils
from server.utils.settings import settings
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.timings import Timings


def parse_min_max_price(price_range_str: str | None):
    """Parse a price range string into a tuple of minimum and maximum prices.

    Args:
        price_range_str (str): The price range string (e.g., "100-200", "300-", "-200").

    Returns:
        tuple: A tuple containing the minimum and maximum prices.
    """
    if price_range_str is None:
        return None, None

    min_price = None
    max_price = None

    # Split the string by '-'
    parts = price_range_str.split("-")

    # Parse the minimum price if it exists
    if len(parts) > 0 and parts[0].strip():
        try:
            min_price = int(parts[0].strip())
        except ValueError:
            pass

    # Parse the maximum price if it exists
    if len(parts) > 1 and parts[1].strip():
        try:
            max_price = int(parts[1].strip())
        except ValueError:
            pass
    if max_price and min_price and max_price < min_price:
        max_price = None

    return min_price, max_price


def parse_agent_response_error(message: BaseMessage):
    try:
        arguments: dict[str, Any] = message.additional_kwargs.get("function_call", {}).get("arguments", "{}")
        if isinstance(arguments, str):
            arguments = json.loads(arguments)
        return arguments.get("status", None), arguments

    except BaseException:
        return None, None


def is_function_call(msg):
    """Helper function for determining if a function was called"""
    return hasattr(msg, "additional_kwargs") and "function_call" in msg.additional_kwargs


def get_flight_detail_from_history(messages: List[BaseMessage], flight_id: str):
    for m in reversed(messages):
        if is_function_call(m) and m.additional_kwargs.get("function_call", {}).get("name") in (
            "FlightSearchResponse",
        ):
            flight_detail = json.loads(m.additional_kwargs["function_call"]["arguments"])
            for flight_choice in flight_detail.get("flight_choices", [{}]):
                if flight_choice.get("id_token_key") == flight_id:
                    return (
                        flight_choice,
                        m.additional_kwargs.get("function_call", {}).get("associated_search_id"),
                        m.additional_kwargs.get("function_call", {}).get("associated_search_source"),
                    )
    return None, None, None


def get_hotel_detail_from_history(*, messages: List[BaseMessage], property_id: int, room_product_id: Optional[str]):
    for m in reversed(messages):
        if is_function_call(m) and m.additional_kwargs.get("function_call", {}).get("name") in (
            "HotelSearchOptionsResponse",
        ):
            hotel_detail = json.loads(m.additional_kwargs["function_call"]["arguments"])
            for hotel_choice in hotel_detail.get("hotel_options", [{}]):
                if hotel_choice.get("property_id") == property_id:
                    if room_product_id is None:
                        return hotel_choice, None
                    selected_room = None
                    if room_product_id is not None and "rooms" in hotel_choice:
                        for room in hotel_choice.get("rooms", []):
                            if room.get("product_id") == room_product_id:
                                selected_room = room
                                break
                    return hotel_choice, selected_room
    return None, None


def csv_from_dict(input: List[dict[str, Any]], dump_filename: str = "csvdata.csv") -> str:
    # Create a CSV version of flight_choices as text
    csv_content = ""

    if len(input) > 0:
        output = StringIO()
        csv_writer = csv.writer(output)

        # Write the header row
        header = input[0].keys()
        csv_writer.writerow(header)

        # Write the data rows
        for dataRow in input:
            csv_writer.writerow(dataRow.values())

        # Get the CSV content as a string
        csv_content = output.getvalue()

        # Close the StringIO object
        output.close()

        # Write the CSV content to a file
        with open(dump_filename, "w", newline="") as file:
            file.write(csv_content)

    return csv_content


def _trim_hotel_options_for_llm(options: List[dict[str, Any]]) -> List[dict[str, Any]]:
    trimed_options = []
    for option in options:
        option.pop("dropoff_url", None)
        option.pop("photos", None)
        option.pop("image_url", None)
        if "rooms" in option:
            rooms = option.get("rooms") or []
            trimed_rooms = []
            for room in rooms:
                room.pop("room_photo")
                trimed_rooms.append(room)

            option["rooms"] = trimed_rooms

        trimed_options.append(option)

    return trimed_options


def get_message_buffer_as_strings(messages: List[BaseMessage] | Sequence[BaseMessage]) -> List[str]:
    buffer_strings = []
    for message in messages:
        # bot messages are either AIMessage or FunctionMessage
        is_bot_message = isinstance(message, AIMessage) or isinstance(message, FunctionMessage)
        if is_bot_message:
            if (
                message.additional_kwargs.get("function_call") is not None
                and message.additional_kwargs.get("function_call", {}).get("arguments") is not None
                and message.additional_kwargs["function_call"]["arguments"] != "{}"
            ):
                arguments_string = message.additional_kwargs["function_call"]["arguments"]
                arguments_dict = None
                try:
                    # Try to load as JSON dict for further processing if needed
                    arguments_dict = json.loads(arguments_string)
                except Exception:
                    arguments_dict = None

                if arguments_dict:
                    if "hotel_options" in arguments_dict:
                        options = arguments_dict.get("hotel_options") or []
                        arguments_dict["hotel_options"] = _trim_hotel_options_for_llm(options)
                        arguments_string = json.dumps(arguments_dict)

            else:
                arguments_string = message.content

            buffer_strings.append(f"assistant {arguments_string}")
        else:
            buffer_strings.append(f"user {message.content}")

    buffer_strings = remove_sequential_duplicates(buffer_strings)

    return buffer_strings


def remove_sequential_duplicates(strings):
    if not strings:
        return []

    # Initialize the result list with the first element
    result = [strings[0]]

    # Iterate through the list starting from the second element
    for current in strings[1:]:
        if current != result[-1]:
            result.append(current)

    return result


def dump_calendar_events_to_s3(user_id: int, events: List[dict[str, Any]]):
    if not events:  # Only upload if events were found
        return
    try:
        timestamp_str = datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
        filename = f"{user_id}_{timestamp_str}"

        asyncio.create_task(
            s3_utils.log_trip_artifact_to_s3(
                root_folder=f"user_{user_id}",  # Use user_id for the S3 folder structure
                file_name=filename,
                content=events,  # Pass the raw events list/dict
                bucket_name=settings.TRIP_ARTIFACT_BUCKET_NAME,  # Use setting for bucket name
                file_type="json",
                extra_path="calendar_events",  # Use a specific folder for calendar events
                queue_for_batch=True,  # Use batching as per s3_utils default behavior
            )
        )
        logger.info(f"Successfully queued raw calendar events upload to S3 for user {user_id}")
    except Exception as e:
        logger.error(f"Failed to queue calendar events upload to S3 for user {user_id}: {e}")


async def load_past_calendar_events(user_id: int):
    user_profile: UserProfile | None = None
    try:
        user_profile = await UserProfile.from_user_id(user_id)

        if user_profile is not None:
            calendar_provider = CalendarProviderManager(user_profile=user_profile)

            if calendar_provider.has_calendar_access():
                today = datetime.now()
                two_years_ago = today - timedelta(days=730)

                events = calendar_provider.get_events(two_years_ago, today)  # keywords=""
                dump_calendar_events_to_s3(events=events, user_id=user_id)
                filtered_travel_calendar_events = calendar_provider.extract_relevant_fields(events)
                event_data_cleanup(filtered_travel_calendar_events)
                logger.info(f"Loaded {len(filtered_travel_calendar_events)} past calendar events for user {user_id}")
                return filtered_travel_calendar_events
        return []
    except Exception as e:
        logger.error(
            f"Failed to load calendar events of past 2 years via {user_profile.last_login_method if user_profile is not None else '<Profile not found>'}: {e}"
        )
        return []


def event_data_cleanup(events):
    # Do some experimental event filtering to reduce payload size and false
    # positive event selection
    for event in events:
        # Get rid of timezones that have city names in them
        event.get("start").pop("timeZone", None)
        event.get("end").pop("timeZone", None)

        # Get rid of html tags to reduce payload size
        description = event.get("description")
        description = remove_html_tags(description)

        # Replace multiple \r\n with a single \r\n
        description = re.sub(r"(\r\n)+", r"\r\n", description)

        # Remove Google Calendar boilerplate message
        description = re.sub(
            r"To see detailed information for automatically created events like this one, use the official Google Calendar app\.\r\nhttps://g\.co/calendar\r\n",
            "",
            description,
            flags=re.IGNORECASE,
        )

        # Remove Gmail event creation boilerplate message
        description = re.sub(
            r"This event was created from an email you received in Gmail\. \r\nhttps://mail\.google\.com/mail\?extsrc=cal&amp;plid=[^\r\n]+\r\n",
            "",
            description,
            flags=re.IGNORECASE,
        )

        # Look for zoom junk and zero it out
        if "zoom" in description.lower():
            description = ""

        description = description.strip()

        event["description"] = description
        # remove calendar id
        del event["id"]
    return events


def remove_html_tags(text):
    # Define a regular expression pattern for HTML tags
    tag_pattern = re.compile(r"<[^>]+>")
    # Use sub() to replace HTML tags with an empty string
    return tag_pattern.sub("", text)


T = TypeVar("T", bound=BaseModel)


def pydantic_delta_update(model_instance: T, updates: dict[str, Any]) -> T:
    """
    Update a Pydantic model instance with a dictionary of updates, handling Enums properly.

    Args:
        model_instance (BaseModel): The Pydantic model instance to update.
        updates (dict[str, Any]): A dictionary containing updates.

    Returns:
        BaseModel: A new instance of the model with updates applied.
    """
    model_fields = model_instance.__class__.model_fields  # Access fields dynamically

    updated_values = {}
    for key, value in updates.items():
        if key in model_instance.model_dump().keys():  # Ensure key exists in model
            field_type = model_fields[key].annotation  # Get field type dynamically

            if get_origin(field_type) is Union:
                possible_types = get_args(field_type)
                field_type = next((t for t in possible_types if isinstance(t, type)), None)

            # Handle Enum conversion
            if isinstance(value, str) and isinstance(field_type, type) and issubclass(field_type, Enum):
                try:
                    value = field_type(value)  # Convert string to Enum
                except ValueError:
                    logger.warning(f"Invalid value for {field_type.__name__}: {value}. Ignored.")
                    continue  # Skip update if invalid enum value
            if isinstance(value, date):
                value = value.strftime("%Y-%m-%d")

            updated_values[key] = value  # Store valid updates

    return model_instance.model_copy(update=updated_values)


async def correct_invalid_date(date_str: str | date | None) -> str | None:
    """Corrects an invalid date string to the nearest valid date.

    Args:
        date_str (str): The input date string (e.g., "2025-02-29", "2025-11-31").

    Returns:
        str: The corrected date string.
    """
    if date_str is not None:
        if isinstance(date_str, date):
            date_str = date_str.strftime("%Y-%m-%d")
        try:
            datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            t = Timings("BAML: CorrectInvalidDate")
            response = await b.CorrectInvalidDate(
                input_date=date_str,
                current_date=get_current_date_string(),
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()
            return response.updated_date
    return date_str


def is_valid_future_date(
    date_input: str | date | None, timezone: str | None, date_baseline: str | date | None = None
) -> bool:
    """Validate that the given date input is a valid date in 'YYYY-MM-DD' format (if string) or a date object, and that it represents a future date.

    Args:
        date_input (str | date | None): The date to validate. Can be a string in 'YYYY-MM-DD' format or a date object.

    Returns:
        bool: True if the date is valid and in the future, False otherwise.
    """
    from datetime import date, datetime

    from dateutil.tz import gettz

    if date_input is None:
        return False

    if isinstance(date_input, date):
        candidate_date = date_input
    else:
        try:
            candidate_date = datetime.strptime(date_input, "%Y-%m-%d").date()
        except ValueError:
            return False

    date_to_compare_with: date = datetime.now(tz=gettz(timezone)).date()
    if date_baseline:
        if isinstance(date_input, date):
            date_to_compare_with = date_baseline  # type: ignore
        elif isinstance(date_input, str):
            date_to_compare_with = datetime.strptime(str(date_baseline), "%Y-%m-%d").date()
    # Check if the candidate date is strictly in the future
    return candidate_date >= date_to_compare_with


def change_to_am_pm_display(date: str):
    """Converts a date string from 24-hour format to 12-hour format with AM/PM.

    Args:
        date (str): The input date string in 'YYYY-MM-DD HH:MM:SS' format.

    Returns:
        str: The date string in 'YYYY-MM-DD hh:MM:SS AM/PM' format.
    """
    return datetime.strptime(date, "%Y-%m-%d %H:%M").strftime("%Y-%m-%d %I:%M%p")


def reconcile_llm_inferred_airline_iata_code(potentail_iata_code: str):
    if potentail_iata_code.upper() in airline_iata_code_to_name:
        return potentail_iata_code

    res = process.extractOne(
        potentail_iata_code,
        airline_name_to_iata_code.keys(),
        scorer=fuzz.token_sort_ratio,
        processor=utils.default_process,
    )

    if res:
        best_match = res[0]
        return airline_name_to_iata_code[best_match]
    return None


def determine_time_category(datetime_string: str):
    try:
        # Parse the datetime string
        # the input is in the format of 2025-06-02T17:30:00 or 2025-07-27 13:56
        #
        dt = (
            datetime.fromisoformat(datetime_string)
            if "T" in datetime_string
            else datetime.strptime(datetime_string, "%Y-%m-%d %H:%M")
        )
    except ValueError:
        # If parsing fails, return None
        return None
    # Define the time ranges
    early_morning = (4, 8)  # 4:00 am to 7:59 am
    morning = (8, 12)  # 8:00 am to 11:59 am
    afternoon = (12, 18)  # 12:00 pm to 5:59 pm
    evening = (18, 20)  # 6:00 pm to 7:59 pm
    night = (20, 24)  # 8:00 pm to 11:59 pm
    late_night = (0, 4)  # 12:00 am to 3:59 am
    # Get the hour from the datetime object
    hour = dt.hour
    # Determine the time category based on the hour
    if early_morning[0] <= hour < early_morning[1]:
        return "early-morning"
    elif morning[0] <= hour < morning[1]:
        return "morning"
    elif afternoon[0] <= hour < afternoon[1]:
        return "afternoon"
    elif evening[0] <= hour < evening[1]:
        return "evening"
    elif night[0] <= hour < night[1]:
        return "night"
    elif late_night[0] <= hour < late_night[1]:
        return "late-night"
    else:
        return None


def determine_is_red_eye(departure_time: str, arrival_time: str) -> bool:
    """
    Determine if a flight is a red-eye based on departure and arrival times.
    Red-eye flights depart in the evening/night and arrive in the morning.

    Args:
        departure_time: Departure time string in format "2025-06-02T17:30:00"
        arrival_time: Arrival time string in format "2025-06-02T08:30:00"

    Returns:
        bool: True if flight is red-eye, False otherwise
    """
    try:
        departure_category = determine_time_category(departure_time)
        arrival_category = determine_time_category(arrival_time)

        departure_is_evening_or_night = departure_category in ["evening", "night", "late-night"]
        arrival_is_morning = arrival_category in ["early-morning", "morning"]

        return departure_is_evening_or_night and arrival_is_morning
    except Exception:
        return False


def generate_flight_keyword(departure_time: str) -> str | None:
    """
    Generate time-based keyword for a flight based on departure time.

    Args:
        departure_time: Departure time string in format "2025-06-02T17:30:00"

    Returns:
        str | None: Time-based keyword or None if none applicable
    """
    try:
        return determine_time_category(departure_time)
    except Exception:
        return None
