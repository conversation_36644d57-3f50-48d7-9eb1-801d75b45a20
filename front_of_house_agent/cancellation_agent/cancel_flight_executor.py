from functools import partial
from typing import Any, Callable, Coroutine

from baml_client.types import CancelFlightConversationStateSchemaWithStep, CancelFlightStep
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.back_of_house_executor.executor import Agent<PERSON>xecutor
from server.database.models.chat_thread import ChatThread
from server.services.trips.bookings import get_bookings_from_airline_confirmation_code, get_bookings_from_pnr_id


class FlightCancelExecutor(AgentExecutor):
    def __init__(
        self,
        flight_helper: FlightBamlHelper,
        message_sender: partial[Coroutine[Any, Any, None]],
        thread: ChatThread,
    ):
        super().__init__(message_sender)
        self.flight_helper = flight_helper
        self.message_sender = message_sender
        self.thread = thread

    async def execute(
        self,
        cancel_flight_state: CancelFlightConversationStateSchemaWithStep,
        message_strs: list,
        completion: Callable[..., Coroutine],
    ):
        if cancel_flight_state.current_step == CancelFlightStep.VERIFY_TICKET_CANCELLABLE:
            return await self.verify_ticket_cancellable(cancel_flight_state, message_strs)
        elif cancel_flight_state.current_step == CancelFlightStep.CANCEL_TICKET:
            return await self.cancel_ticket(cancel_flight_state, message_strs, completion)

    async def verify_ticket_cancellable(
        self,
        cancel_flight_state: CancelFlightConversationStateSchemaWithStep,
        messsage_strs: list[str],
    ):
        booking = None
        if cancel_flight_state.airline_confirmation_id:
            booking = await get_bookings_from_pnr_id(cancel_flight_state.airline_confirmation_id)
        elif cancel_flight_state.airline_confirmation_number:
            booking = await get_bookings_from_airline_confirmation_code(cancel_flight_state.airline_confirmation_number)
        assert booking, "Booking not found"
        await self.message_sender(
            message={
                "type": "search_update",
                "text": "Let me check to see if the flight can be canceled. This can take a moment. Thanks for your patience.",
                "isBotMessage": True,
                "expectResponse": False,
            }
        )
        trip_id = str(booking.get("flight", {}).get("trip_id"))
        confirmation_id = str(booking.get("flight", {}).get("confirmation_id"))
        message = await self.flight_helper.verify_ticket_cancellable(
            confirmation_id,
            trip_id,
            messsage_strs,
        )

        return message

    async def cancel_ticket(
        self,
        cancel_flight_state: CancelFlightConversationStateSchemaWithStep,
        message_strs: list[str],
        completion: Callable[..., Coroutine],
    ):
        booking = None
        if cancel_flight_state.airline_confirmation_id:
            booking = await get_bookings_from_pnr_id(cancel_flight_state.airline_confirmation_id)
        elif cancel_flight_state.airline_confirmation_number:
            booking = await get_bookings_from_airline_confirmation_code(cancel_flight_state.airline_confirmation_number)

        assert booking, "Booking not found"
        await self.message_sender(
            message={
                "type": "search_update",
                "text": "Canceling your flight booking  -  this will just take a minute.",
                "isBotMessage": True,
                "expectResponse": False,
            }
        )
        trip_id = str(booking.get("flight", {}).get("trip_id"))
        confirmation_id = str(booking.get("flight", {}).get("confirmation_id"))

        message = await self.flight_helper.cancel_flight(
            trip_id,
            confirmation_id,
            cancel_flight_state.cancel_option_id or "",
            message_strs,
        )

        await self.message_sender(
            message={
                "type": "itinerary_update",
            }
        )
        await self.message_sender(
            message={
                "type": "trip_update",
            }
        )

        await completion()
        return message
