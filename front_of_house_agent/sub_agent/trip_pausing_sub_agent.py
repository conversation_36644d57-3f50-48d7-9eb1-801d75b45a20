import json
from functools import partial
from typing import Any

from langchain_core.messages import AIMessage, message_to_dict

import front_of_house_agent.front_of_house_agent as fha
from agent.agent import StopResponse
from baml_client import b
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.utils.mongo_connector import trip_context_v2_collection
from server.utils.settings import settings
from virtual_travel_agent.helpers import get_current_date_string


async def handle_trip_pausing(foh: "fha.FrontOfHouseAgent", message_buffer_strs: list[str]) -> dict[str, Any]:
    """Handle trip pausing by saving current progress to a new trip."""

    has_saveable_content = (
        foh.travel_context.selected_outbound_flight is not None
        or foh.travel_context.selected_return_flight is not None
        or foh.travel_context.hotel_select_result.property_id is not None
        or bool(foh.travel_context.selected_hotel_for_segment)
        or bool(foh.travel_context.selected_hotel_option_for_segment)
    )

    travel_context_dict = foh.travel_context.to_dict()
    travel_context_dict.update(foh.user_preferences.model_dump(exclude_none=True))

    if not has_saveable_content:
        inquiry_response = await foh.process_streaming(
            partial(
                b.stream.ConverseTripSaving,
                travel_context=json.dumps(travel_context_dict, default=str),
                has_saveable_content=has_saveable_content,
                messages=message_buffer_strs,
                current_date=get_current_date_string(foh.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                user_name=foh.user.name,
            ),
            lambda x: x.agent_response,
            lambda x: x.agent_response,
        )

        if isinstance(inquiry_response, StopResponse):
            return {
                "messages": [
                    AIMessage(
                        content=inquiry_response.last_text or "",
                        additional_kwargs={"is_stopped": True},
                    )
                ],
            }
        else:
            return {
                "messages": [
                    AIMessage(
                        content=inquiry_response.agent_response,
                    )
                ],
            }

    await foh.websocket_send_message(
        message={
            "type": "search_update",
            "text": "Saving your trip so that you can pick back up later.",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    try:
        new_thread = ChatThread(
            users_id=foh.user.id,
            title=f"Saved {foh.thread.title} Trip",
            extra={"savedFromTrip": foh.thread.id},
            frequent_flyer_attempted_flight=foh.thread.frequent_flyer_attempted_flight,
        )
        await ChatThread.new_chat_thread(new_thread)

        itinerary_response = await b.ConverseTripSaving(
            travel_context=json.dumps(travel_context_dict, default=str),
            has_saveable_content=has_saveable_content,
            messages=message_buffer_strs,
            current_date=get_current_date_string(foh.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            user_name=foh.user.name,
        )

        new_message = AIMessage(content=itinerary_response.agent_response)
        new_checkpoint = Checkpoint(
            thread_id=new_thread.id,
            data=message_to_dict(new_message),
            input_tokens=None,
            output_tokens=None,
        )

        await Checkpoint.new_checkpoint(new_checkpoint)

        travel_context = await trip_context_v2_collection.find_one({"thread_id": foh.thread.id})
        if travel_context:
            if "_id" in travel_context:
                travel_context.pop("_id")
            travel_context["thread_id"] = new_thread.id
            await trip_context_v2_collection.update_one(
                {"thread_id": new_thread.id}, {"$set": travel_context}, upsert=True
            )

        await foh.websocket_send_message(
            message={
                "type": "trip_update",
            }
        )

        return {
            "messages": [
                AIMessage(
                    content="I've saved your trip progress for later. You can find it in your saved trips and continue planning whenever you're ready!",
                )
            ],
        }

    except Exception:
        return {
            "messages": [
                AIMessage(
                    content="I encountered an issue while saving your trip. Please try again or contact support if the problem persists.",
                )
            ],
        }
