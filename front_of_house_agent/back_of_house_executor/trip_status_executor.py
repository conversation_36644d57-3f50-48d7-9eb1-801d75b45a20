import asyncio
import json

from langchain_core.messages import AIMessage

import front_of_house_agent.front_of_house_agent as fha
from baml_client import b
from baml_client.types import FlightStatusCheckResponse
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.spotnana_client import SpotnanaClient
from server.services.trips.bookings import get_bookings_from_airline_confirmation_code


async def handle_flight_status(foh_agent: "fha.FrontOfHouseAgent", status_request: FlightStatusCheckResponse):
    message = None
    if (not status_request.confirmation_id) and (not status_request.airline_confirmation_number):
        message = AIMessage(
            content="Please provide a confirmation ID or an airline confirmation number to check the flight status."
        )
    else:
        trip_id = None
        if status_request.airline_confirmation_number:
            booking = await get_bookings_from_airline_confirmation_code(status_request.airline_confirmation_number)
            trip_id = booking.get("flight", {}).get("trip_id")

        assert trip_id, "Trip ID should not be None"

        asyncio.create_task(
            foh_agent.websocket_send_message(
                message={
                    "type": "search_update",
                    "isBotMessage": True,
                    "expectResponse": False,
                    "text": "Let me check the flight status for you. Please wait.",
                }
            )
        )

        trip_details = await SpotnanaClient.get_trip_details(trip_id)

        if not trip_details:
            message = AIMessage(
                content="No trip details found for the provided confirmation ID or airline confirmation number."
            )
        else:
            summary_string = await b.SummarizeFlightInfo(json.dumps(trip_details))
            message = AIMessage(content=summary_string)

    await foh_agent.persist_messages([message])
    websocket_message = await map_websocket_message(
        message,
        None,
        None,
    )

    await foh_agent.websocket_send_message(message={**websocket_message[0]})
