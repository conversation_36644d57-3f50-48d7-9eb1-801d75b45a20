import asyncio
import json
from functools import partial
from typing import Any, Callable, Coroutine, Dict, Optional

from langchain_core.messages import AIMessage, BaseMessage

from baml_client import b
from baml_client.types import (
    FlightPlanningStep,
    FlightPlanResponse,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightSegment,
    FlightType,
    HotelPlanningStep,
    HotelPlanResponse,
    HotelSearchAdditionalCriteria,
    HotelSearchCoreCriteria,
    HotelSegment,
    HotelSelectResult,
    SeatSelectionForFlight,
)
from flight_agent.flight_booking_util import FlightBookingUtil
from flight_agent.flights_helper import FlightBamlHelper
from flight_agent.flights_tools import FlightSearchTools
from front_of_house_agent import flight_utils
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.executor import AgentExecutor
from front_of_house_agent.back_of_house_executor.flight_executor_functions import (
    flight_baggage_check,
    flight_booking_v2,
    flight_checkout,
    flight_credits_check,
    flight_validation,
    search_flights,
)
from front_of_house_agent.back_of_house_executor.hotel_executor_functions import (
    hotel_booking,
    is_hotel_ready_to_book,
    search_hotels,
    validate_hotel,
)
from front_of_house_agent.back_of_house_executor.seat_selection_executor import check_available_seats
from front_of_house_agent.common_models import (
    EnrichedFlightSelectResult,
    FlightOption,
    FlightSearchParams,
    FlightSearchSource,
    FlightSearchType,
    HotelValidationResult,
)
from front_of_house_agent.serp_flight_helper import SerpFlightSearchHelper
from hotel_agent.booking_dot_com_tools import BookingTools
from hotel_agent.hotels_helper import HotelsHelper
from llm_utils.llm_utils import (
    get_flight_detail_from_history,
    parse_min_max_price,
    reconcile_llm_inferred_airline_iata_code,
)
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.google_maps_api.get_lat_long import get_lat_long_from_loc_str
from server.services.trips.bookings import get_bookings_of_status
from server.services.user.user_activity import update_user_activity
from server.services.user.user_preferences import (
    SearchType,
    increment_triggered_search_amount,
)
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.logger import logger
from server.utils.mongo_connector import preferred_airline_per_airport_collection, trip_context_v2_collection
from server.utils.settings import settings
from virtual_travel_agent.helpers import console_masks, get_current_date_string

flight_mask = console_masks["flight"]
hotel_mask = console_masks["hotel"]
other_mask = console_masks["other"]


class TripPlanExecutor(AgentExecutor):
    def __init__(
        self,
        hotel_helper: HotelsHelper,
        flight_helper: FlightBamlHelper,
        message_sender: partial[Coroutine[Any, Any, None]],
        thread: ChatThread,
        timezone: str | None,
        user: User,
        user_profile: UserProfile | None,
    ):
        super().__init__(message_sender)
        self.hotel_helper = hotel_helper
        self.flight_helper = flight_helper
        self.message_sender = message_sender
        self.thread = thread
        self.timezone = timezone
        self.user = user
        self.booking_dot_com_tools = BookingTools(user=user)
        self.flight_booking_util = FlightBookingUtil(thread=thread, user_profile=user_profile, user=user)

        self.serp_flight_search = SerpFlightSearchHelper(user=user, thread=thread, timezone=timezone)

        self.flight_choice_from_outbound_search: dict[str, Any] | None = None

    async def execute_flight_plan(
        self,
        trip_plan_state: FlightPlanResponse,
        flight_search_criteria_core: FlightSearchCoreCriteria,
        flight_segments: list[FlightSegment] | None,
        flight_search_criteria_additional: FlightSearchAdditionalCriteria,
        flight_select_result: EnrichedFlightSelectResult,
        seat_selection_for_flights: list[SeatSelectionForFlight] | None,
        selected_outbound_flight: Optional[FlightOption],
        selected_return_flight: Optional[FlightOption],
        selected_flight_for_segment: Optional[Dict[str, FlightOption]],
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
        user_responsed_entry_requirment: bool | None,
        user_provided_citizenship: list[str] | None,
        extral_params: Dict[str, Any],
        callback_handlers: Dict[str, Callable],
        current_working_on_segment: int | None,
    ):
        """Execute flight plan steps."""
        step = trip_plan_state.current_step
        step_name = step.name if isinstance(step, FlightPlanningStep) else step
        task_id = f"{self.thread.id}_{step_name}"

        if self.task_map.get(task_id) and not self.task_map[task_id].done():
            if step_name == FlightPlanningStep.FLIGHT_BOOKING.name:
                self.schedule_send_message(
                    message={
                        "type": "search_update",
                        "text": "I'm waiting to get the flight booking confirmation. It shouldn't be too long.",
                        "isBotMessage": True,
                        "expectResponse": False,
                    },
                )
                return None
            logger.info(f"Cancelling task: {task_id}", other_mask)
            self.task_map[task_id].cancel()

        task = None
        if (
            step_name == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name
            or step_name == FlightPlanningStep.FLIGHT_SEARCH.name
        ):
            default_airline_brands_from_airport_code = None
            reconciled_preferred_airline_codes = None
            if flight_search_criteria_additional.preferred_airline_codes:
                reconciled_preferred_airline_codes: list[str] | None = [
                    reconcilled
                    for airline_code in flight_search_criteria_additional.preferred_airline_codes
                    if (reconcilled := reconcile_llm_inferred_airline_iata_code(airline_code)) is not None
                ]
                logger.info(
                    f"Reconciled preferred airline codes: {reconciled_preferred_airline_codes} from {flight_search_criteria_additional.preferred_airline_codes}",
                )

            if not reconciled_preferred_airline_codes:
                default_airline_brands_from_airport_dict = await preferred_airline_per_airport_collection.find_one(
                    {
                        "IATA_CODE": flight_search_criteria_core.departure_airport_code,
                    }
                )

                if default_airline_brands_from_airport_dict:
                    default_airline_brands_from_airport_code = default_airline_brands_from_airport_dict.get(
                        "AIRLINE_CODES"
                    )
            selected_outbound_flight_id = flight_select_result.selected_outbound_flight_id or (
                next(
                    (
                        x.selected_flight_id
                        for x in (flight_select_result.selected_flight_for_segment or [])
                        if x.segment_index
                        == (current_working_on_segment - 1 if current_working_on_segment is not None else -1)
                    ),
                    None,
                )
            )
            search_id = None
            if selected_outbound_flight_id:
                candidate = get_flight_detail_from_history(
                    extral_params.get("messages") or [], selected_outbound_flight_id
                )
                if candidate:
                    search_id = candidate[1]

            is_us_domestic = FlightSearchTools.is_us_domestic_flight(
                flight_search_criteria_core.departure_airport_code,
                flight_search_criteria_core.arrival_airport_code,
                flight_search_criteria_core.is_departure_iata_city_code,
                flight_search_criteria_core.is_arrival_iata_city_code,
            )

            is_premium_cabin_search = flight_utils.is_premium_cabin(flight_search_criteria_additional.cabin)
            include_default_airline_brands = (
                is_us_domestic
                and (not is_premium_cabin_search)
                and (not flight_search_criteria_additional.ignore_all_airlines)
            )

            travel_context_dict = flight_search_criteria_additional.model_dump(exclude_none=True)

            travel_context_dict["refundable"] = (
                "not_specified"
                if flight_search_criteria_additional.refundable is None
                else str(flight_search_criteria_additional.refundable)
            )

            flight_search_dict: FlightSearchParams = {
                "current_step": "OUTBOUND_FLIGHT_SEARCH",
                "search_purpose": step_name,
                "departure_airport_code": flight_search_criteria_core.departure_airport_code,
                "is_departure_iata_city_code": flight_search_criteria_core.is_departure_iata_city_code,
                "arrival_airport_code": flight_search_criteria_core.arrival_airport_code,
                "is_arrival_iata_city_code": flight_search_criteria_core.is_arrival_iata_city_code,
                "outbound_date": flight_search_criteria_core.outbound_date,
                "return_date": None
                if flight_search_criteria_core.flight_type == FlightType.OneWay
                else flight_search_criteria_core.return_date,
                "flight_type": flight_search_criteria_core.flight_type,
                "preferred_airline_codes": reconciled_preferred_airline_codes,
                "default_airline_brands": default_airline_brands_from_airport_code
                if include_default_airline_brands
                else None,
                "preferred_cabin": flight_search_criteria_additional.cabin,
                "travel_context": json.dumps(travel_context_dict),
                "number_of_stops": flight_search_criteria_additional.number_of_stops,
                "search_id": search_id,
                "selected_outbound_flight_id": selected_outbound_flight_id,
                "outbound_arrival_time": flight_search_criteria_additional.outbound_arrival_time,
                "outbound_departure_time": flight_search_criteria_additional.outbound_departure_time,
                "return_arrival_time": flight_search_criteria_additional.return_arrival_time,
                "return_departure_time": flight_search_criteria_additional.return_departure_time,
                "search_segments": flight_segments,
            }
            # cancel the previous task if it exists
            self.cancel_task(task_id)
            task = self.wrap_task(
                asyncio.create_task(
                    search_flights(
                        self,
                        step_name == FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.name
                        or (
                            step_name == "FLIGHT_SEARCH"
                            and (current_working_on_segment is None or current_working_on_segment == 0)
                        ),
                        flight_search_dict,
                        extral_params.get("message_strs") or [],
                        FlightSearchType.ONE_WAY
                        if flight_search_criteria_core.flight_type == FlightType.OneWay
                        else FlightSearchType.ROUND_TRIP
                        if flight_search_criteria_core.flight_type == FlightType.RoundTrip
                        else FlightSearchType.MULTI_CITY,
                        flight_search_criteria_additional,
                        is_premium_cabin_search,
                        message_persistor,
                        flight_select_result.search_source,
                        current_working_on_segment,
                        None,
                        callback_handlers.get(step_name),
                    ),
                    name=task_id,
                )
            )

            asyncio.create_task(
                self.record_search_trigger(SearchType.FLIGHT),
                name=f"{task_id}_record_search_trigger",
            )

        elif step_name == FlightPlanningStep.RETURN_FLIGHT_SEARCH.name:
            reconciled_preferred_airline_codes = None
            if flight_search_criteria_additional.preferred_airline_codes:
                reconciled_preferred_airline_codes: list[str] | None = [
                    reconcilled
                    for airline_code in flight_search_criteria_additional.preferred_airline_codes
                    if (reconcilled := reconcile_llm_inferred_airline_iata_code(airline_code)) is not None
                ]
                logger.info(
                    f"Reconciled preferred airline codes: {reconciled_preferred_airline_codes} from {flight_search_criteria_additional.preferred_airline_codes}",
                )
            default_airline_brands_from_airport_code = None
            if not reconciled_preferred_airline_codes:
                default_airline_brands_from_airport_dict = await preferred_airline_per_airport_collection.find_one(
                    {
                        "IATA_CODE": flight_search_criteria_core.arrival_airport_code,
                    }
                )

                if default_airline_brands_from_airport_dict:
                    default_airline_brands_from_airport_code = default_airline_brands_from_airport_dict.get(
                        "AIRLINE_CODES"
                    )
            is_premium_cabin_search = flight_utils.is_premium_cabin(flight_search_criteria_additional.cabin)
            is_us_domestic = FlightSearchTools.is_us_domestic_flight(
                flight_search_criteria_core.departure_airport_code, flight_search_criteria_core.arrival_airport_code
            )
            include_default_airline_brands = (
                is_us_domestic
                and (not is_premium_cabin_search)
                and (not flight_search_criteria_additional.ignore_all_airlines)
            )
            travel_context_dict = flight_search_criteria_additional.model_dump(exclude_none=True)

            travel_context_dict["refundable"] = (
                "not_specified"
                if flight_search_criteria_additional.refundable is None
                else str(flight_search_criteria_additional.refundable)
            )

            flight_search_dict: FlightSearchParams = {
                "current_step": "RETURN_FLIGHT_SEARCH",
                "search_purpose": step_name,
                "departure_airport_code": flight_search_criteria_core.departure_airport_code,
                "is_departure_iata_city_code": flight_search_criteria_core.is_departure_iata_city_code,
                "arrival_airport_code": flight_search_criteria_core.arrival_airport_code,
                "is_arrival_iata_city_code": flight_search_criteria_core.is_arrival_iata_city_code,
                "outbound_date": flight_search_criteria_core.outbound_date,
                "return_date": flight_search_criteria_core.return_date,
                "flight_type": flight_search_criteria_core.flight_type,
                "search_id": flight_select_result.search_id,
                "selected_outbound_flight_id": flight_select_result.selected_outbound_flight_id,
                "preferred_airline_codes": reconciled_preferred_airline_codes,
                "default_airline_brands": default_airline_brands_from_airport_code
                if include_default_airline_brands
                else None,
                "preferred_cabin": flight_search_criteria_additional.cabin,
                "travel_context": json.dumps(travel_context_dict),
                "number_of_stops": flight_search_criteria_additional.number_of_stops,
                "outbound_arrival_time": flight_search_criteria_additional.outbound_arrival_time,
                "outbound_departure_time": flight_search_criteria_additional.outbound_departure_time,
                "return_arrival_time": flight_search_criteria_additional.return_arrival_time,
                "return_departure_time": flight_search_criteria_additional.return_departure_time,
                "search_segments": flight_segments,
            }
            task = self.wrap_task(
                asyncio.create_task(
                    search_flights(
                        self,
                        False,
                        flight_search_dict,
                        extral_params.get("message_strs") or [],
                        FlightSearchType.ONE_WAY
                        if flight_search_criteria_core.flight_type == FlightType.OneWay
                        else FlightSearchType.ROUND_TRIP
                        if flight_search_criteria_core.flight_type == FlightType.RoundTrip
                        else FlightSearchType.MULTI_CITY,
                        flight_search_criteria_additional,
                        is_premium_cabin_search,
                        message_persistor,
                        flight_select_result.search_source,
                        current_working_on_segment,
                        selected_outbound_flight,
                        callback_handlers.get(step_name),
                    ),
                    name=task_id,
                )
            )

            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.FLIGHT_SEARCHED,
                user_id=str(self.user.id),
                user_email=self.user.email,
                event_properties={
                    "origin": flight_search_criteria_core.departure_airport_code,
                    "destination": flight_search_criteria_core.arrival_airport_code,
                    "departure_date": flight_search_criteria_core.outbound_date,
                },
            )

        elif step_name == FlightPlanningStep.FLIGHT_BAGGAGE_CHECK.name:
            spotnana_flight_id = (
                flight_select_result.matched_flight_id
                or flight_select_result.selected_return_flight_id
                or flight_select_result.selected_outbound_flight_id
            )
            if flight_search_criteria_core.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if flight_select_result.selected_flight_for_segment
                    else None
                )
            if selected_outbound_flight:
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_baggage_check(
                            self,
                            selected_outbound_flight,
                            selected_return_flight,
                            selected_flight_for_segment,
                            flight_select_result,
                            message_persistor,
                            callback_handlers.get(step_name),
                            flight_select_result.search_source,
                            flight_select_result.search_id,
                            spotnana_flight_id,
                        ),
                        name=task_id,
                    )
                )

        elif step_name == FlightPlanningStep.FLIGHT_CHECKOUT.name:
            spotnana_flight_id = (
                flight_select_result.selected_return_flight_id or flight_select_result.selected_outbound_flight_id
            )
            if flight_search_criteria_core.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if flight_select_result.selected_flight_for_segment
                    else None
                )

            if selected_outbound_flight:
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_checkout(
                            self,
                            selected_outbound_flight,
                            selected_return_flight,
                            selected_flight_for_segment,
                            flight_select_result,
                            extral_params.get("preferred_seat_types") or [],
                            callback_handlers.get(step_name),
                            message_persistor,
                            flight_select_result.search_source,
                            flight_select_result.search_id,
                            spotnana_flight_id,
                        ),
                        name=task_id,
                    )
                )
        elif step_name == FlightPlanningStep.FLIGHT_VALIDATION.name:
            spotnana_flight_id = (
                flight_select_result.matched_flight_id
                or flight_select_result.selected_return_flight_id
                or flight_select_result.selected_outbound_flight_id
            )
            if flight_search_criteria_core.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if flight_select_result.selected_flight_for_segment
                    else None
                )
            if (
                flight_select_result.search_source is None
                or flight_select_result.search_source == FlightSearchSource.SERP
            ) and not flight_select_result.matched_flight_id:
                logger.warning(
                    "Flight validation step has matched_flight_id, let's go back to checkout",
                    other_mask,
                )

                assert selected_outbound_flight is not None, (
                    "in flight validation step, selected outbound flight should not be None"
                )
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_checkout(
                            self,
                            selected_outbound_flight,
                            selected_return_flight,
                            selected_flight_for_segment,
                            flight_select_result,
                            extral_params.get("preferred_seat_types") or [],
                            callback_handlers.get(FlightPlanningStep.FLIGHT_CHECKOUT.name),
                            message_persistor,
                            flight_select_result.search_source,
                            flight_select_result.search_id,
                            spotnana_flight_id,
                        ),
                        name=task_id,
                    )
                )
            else:
                if selected_outbound_flight:
                    task = self.wrap_task(
                        asyncio.create_task(
                            flight_validation(
                                self,
                                selected_outbound_flight,
                                selected_return_flight,
                                selected_flight_for_segment,
                                flight_select_result,
                                seat_selection_for_flights or [],
                                flight_select_result.search_source,
                                flight_select_result.search_id,
                                spotnana_flight_id,
                                message_persistor,
                            ),
                            name=task_id,
                        )
                    )

        elif step_name == FlightPlanningStep.CHECK_SEAT_AVAILABILITY.name:
            spotnana_flight_id = (
                flight_select_result.matched_flight_id
                or flight_select_result.selected_return_flight_id
                or flight_select_result.selected_outbound_flight_id
            )
            if flight_search_criteria_core.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if flight_select_result.selected_flight_for_segment
                    else None
                )
            assert isinstance(trip_plan_state, FlightPlanResponse)
            task = self.wrap_task(
                asyncio.create_task(
                    check_available_seats(
                        self,
                        flight_select_result.search_id,
                        spotnana_flight_id,
                        extral_params.get("preferred_seat_types") or [],
                        self.user.email,
                        extral_params.get("message_strs") or [],
                        trip_plan_state.availability_param,
                        message_persistor,
                    ),
                    name=task_id,
                )
            )
        elif step_name == FlightPlanningStep.FLIGHT_CREDIT_CHECK.name:
            if selected_outbound_flight:
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_credits_check(
                            self,
                            selected_outbound_flight,
                            selected_return_flight,
                            message_persistor,
                        ),
                        name=task_id,
                    )
                )

        elif step_name == FlightPlanningStep.FLIGHT_BOOKING.name:
            spotnana_flight_id = (
                flight_select_result.selected_return_flight_id or flight_select_result.selected_outbound_flight_id
            )
            if flight_search_criteria_core.flight_type == FlightType.MultiLegs:
                spotnana_flight_id = (
                    flight_select_result.selected_flight_for_segment[-1].selected_flight_id
                    if flight_select_result.selected_flight_for_segment
                    else None
                )
            if selected_outbound_flight:
                task = self.wrap_task(
                    asyncio.create_task(
                        flight_booking_v2(
                            self,
                            selected_outbound_flight,
                            selected_return_flight,
                            selected_flight_for_segment,
                            user_responsed_entry_requirment,
                            user_provided_citizenship,
                            flight_search_criteria_core.is_international_flight_trip or False,
                            extral_params.get("preferred_seat_types") or [],
                            callback_handlers.get(FlightPlanningStep.FLIGHT_VALIDATION.name),
                            flight_select_result,
                            seat_selection_for_flights or [],
                            flight_search_criteria_core.departure_airport_code,
                            flight_search_criteria_core.arrival_airport_code,
                            message_persistor,
                            callback_handlers.get(step_name),
                            flight_select_result.search_source,
                            flight_select_result.search_id,
                            spotnana_flight_id,
                        ),
                        name=task_id,
                    )
                )

                # Get airline preferences for tracking (reuse the same logic)
                default_airline_brands_from_airport_code = None
                if not flight_search_criteria_additional.preferred_airline_codes:
                    default_airline_dict = await preferred_airline_per_airport_collection.find_one(
                        {"IATA_CODE": flight_search_criteria_core.departure_airport_code}
                    )
                    if default_airline_dict:
                        default_airline_brands_from_airport_code = default_airline_dict.get("AIRLINE_CODES")

                selected_flight = selected_return_flight or selected_outbound_flight
                ticket_price_usd = selected_flight.total_price if selected_flight else None

                await TrackingManager.log_event_in_background(
                    event_type=TrackingEvent.FLIGHT_BOOKED,
                    user_id=str(self.user.id),
                    user_email=self.user.email,
                    event_properties={
                        "airline": default_airline_brands_from_airport_code,
                        "ticket_price_usd": ticket_price_usd,
                    },
                )

        return task

    async def execute_hotel_plan(
        self,
        trip_plan_state: HotelPlanResponse,
        hotel_segments: list[HotelSegment] | None,
        hotel_search_criteria_core: HotelSearchCoreCriteria,
        hotel_search_criteria_additional: HotelSearchAdditionalCriteria,
        hotel_validation_result: HotelValidationResult,
        hotel_validation_result_for_segment: Optional[Dict[str, HotelValidationResult]],
        hotel_select_result: HotelSelectResult,
        selected_hotel_for_segment: Optional[Dict[str, HotelSelectResult]],
        selected_hotel_option_for_segment: Optional[Dict[str, Dict[str, Any]]],
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
        extral_params: Dict[str, Any],
        on_hotel_validated: Callable[
            [str | None, float | None, dict[str, HotelValidationResult] | None], Coroutine[Any, None, None]
        ]
        | None,
        on_hotel_booked: Callable[[], Coroutine[Any, None, None]] | None,
        is_mobile: bool = False,
    ):
        """Execute hotel plan steps."""
        step = trip_plan_state.current_step
        step_name = step.name if isinstance(step, HotelPlanningStep) else step
        task_id = f"{self.thread.id}_{step_name}"

        if self.task_map.get(task_id) and not self.task_map[task_id].done():
            if (
                step_name == HotelPlanningStep.HOTEL_SEARCH.name
                and trip_plan_state.updated_hotel_search_core_criteria is None
                and trip_plan_state.updated_hotel_search_additional_criteria is None
            ):
                return None

            if step_name == HotelPlanningStep.HOTEL_BOOKING.name:
                self.schedule_send_message(
                    message={
                        "type": "search_update",
                        "text": "I'm waiting to get the hotel reservation confirmation. It shouldn't be too long.",
                        "isBotMessage": True,
                        "expectResponse": False,
                    },
                )
                return None

            logger.info(f"Cancelling task: {task_id}", other_mask)
            self.task_map[task_id].cancel()

        task = None
        if step_name == HotelPlanningStep.HOTEL_SEARCH.name:
            min_price, max_price = parse_min_max_price(hotel_search_criteria_additional.price_range)
            hotel_search_dict = {
                "check_in_date": hotel_search_criteria_core.check_in_date,
                "check_out_date": hotel_search_criteria_core.check_out_date,
                "hotel_search_radius": hotel_search_criteria_core.hotel_search_radius,
                "travel_context": (hotel_search_criteria_core.model_dump_json(exclude_none=True) or "")
                + ", "
                + (hotel_search_criteria_additional.model_dump_json(exclude_none=True) or ""),
                "messages": extral_params.get("message_strs") or [],
                "preferred_payment_timings": hotel_search_criteria_additional.preferred_payment_timings,
                "min_price": min_price,
                "max_price": max_price,
                "is_mobile": is_mobile,
            }
            if hotel_search_criteria_additional.brand_hotel_preferences:
                hotel_search_dict["brand_hotel_preferences"] = hotel_search_criteria_additional.brand_hotel_preferences

            location_name = None
            if hotel_search_criteria_additional.location_neighborhoods_districts_landmarks:
                location_name = hotel_search_criteria_additional.location_neighborhoods_districts_landmarks
            elif hotel_search_criteria_additional.street_name:
                location_name = hotel_search_criteria_additional.street_name

            if hotel_search_criteria_core.city:
                if location_name:
                    location_name = f"{location_name}, {hotel_search_criteria_core.city}"
                else:
                    location_name = f"Downtown, {hotel_search_criteria_core.city}"

            lat_long_str = None
            if location_name:
                lat_long_str = await get_lat_long_from_loc_str(location_name)
                logger.info(
                    f"Location lat long of {location_name}:\n"
                    f"from Google Maps API: {lat_long_str}\n"
                    f"from LLM: {hotel_search_criteria_core.location_latitude_longitude}",
                    hotel_mask,
                )
                if lat_long_str:
                    hotel_search_criteria_core.location_latitude_longitude = lat_long_str
                    hotel_search_dict["location_latitude_longitude"] = lat_long_str

            if lat_long_str is None:
                logger.warning(
                    f"Failed to get lat long for {location_name}, using LLM lat long: {hotel_search_criteria_core.location_latitude_longitude}",
                    hotel_mask,
                )
                hotel_search_dict["location_latitude_longitude"] = (
                    hotel_search_criteria_core.location_latitude_longitude
                )

            task = self.wrap_task(
                asyncio.create_task(
                    search_hotels(
                        self,
                        hotel_select_result,
                        hotel_search_dict,
                        extral_params.get("messages") or [],
                        message_persistor,
                        self.timezone,
                    ),
                    name=task_id,
                )
            )

            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.HOTEL_SEARCHED,
                user_id=str(self.user.id),
                user_email=self.user.email,
                event_properties={
                    "city": hotel_search_criteria_core.city,
                    "check_in": hotel_search_criteria_core.check_in_date,
                    "check_out": hotel_search_criteria_core.check_out_date,
                    "location_name": location_name,
                },
            )

            asyncio.create_task(
                self.record_search_trigger(SearchType.HOTEL),
                name=f"{task_id}_record_search_trigger",
            )

            await trip_context_v2_collection.update_one(
                {"thread_id": self.thread.id},
                {
                    "$set": {
                        "hotel_search_core_criteria": hotel_search_criteria_core.model_dump(),
                    }
                },
                upsert=True,
            )

        elif step_name == HotelPlanningStep.HOTEL_VALIDATION.name:
            if hotel_select_result.property_id and hotel_select_result.room_product_id:
                task = self.wrap_task(
                    asyncio.create_task(
                        validate_hotel(
                            self,
                            hotel_select_result,
                            hotel_search_criteria_core,
                            message_persistor,
                            on_hotel_validated,
                            hotel_segments,
                            selected_hotel_for_segment,
                            extral_params.get("message_strs") or [],
                        ),
                        name=task_id,
                    )
                )

        elif step_name == HotelPlanningStep.HOTEL_BOOKING.name:
            if await is_hotel_ready_to_book(
                hotel_segments,
                hotel_select_result,
                hotel_validation_result,
                hotel_validation_result_for_segment,
                selected_hotel_for_segment,
            ):
                task = self.wrap_task(
                    asyncio.create_task(
                        hotel_booking(
                            self,
                            hotel_search_criteria_core.check_in_date or "",
                            hotel_search_criteria_core.check_out_date or "",
                            hotel_search_criteria_core.city,
                            hotel_select_result,
                            hotel_validation_result,
                            message_persistor,
                            extral_params.get("messages") or [],
                            on_hotel_booked,
                            hotel_segments,
                            hotel_validation_result_for_segment,
                            selected_hotel_for_segment,
                            selected_hotel_option_for_segment,
                        ),
                        name=task_id,
                    )
                )
                # Track hotel booking event
                await TrackingManager.log_event_in_background(
                    event_type=TrackingEvent.HOTEL_BOOKED,
                    user_id=str(self.user.id),
                    user_email=self.user.email,
                    event_properties={
                        "total_price_usd": hotel_validation_result.validated_price,
                        "hotel_name": hotel_select_result.hotel_name,
                    },
                )

            else:
                self.schedule_send_message(
                    message={
                        "type": "search_update",
                        "text": "I need to try that again. The system came back with a validation error. Retrying now.",
                        "isBotMessage": True,
                        "expectResponse": False,
                    }
                )

                task = self.wrap_task(
                    asyncio.create_task(
                        validate_hotel(
                            self,
                            hotel_select_result,
                            hotel_search_criteria_core,
                            message_persistor,
                            on_hotel_validated,
                            hotel_segments,
                            selected_hotel_for_segment,
                            extral_params.get("message_strs") or [],
                        ),
                        name=task_id,
                    )
                )

        if task:
            logger.info(f"Created task: {task_id}", other_mask)
            return task
        return None

    def schedule_send_message(self, message: dict[str, Any]):
        asyncio.create_task(self.message_sender(message=message))

    async def send_post_booking_message(
        self,
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    ):
        # limit to 2 bookings because for now users can only have 2 booked bookings one for flight and one for hotel
        bookings = await get_bookings_of_status(self.thread, ["booked", "pending"], limit=10)
        flight_booked = False
        hotel_booked = False
        if bookings:
            for booking in bookings:
                if booking.type == "flight":
                    flight_booked = True
                elif booking.type == "accommodations":
                    hotel_booked = True

        logger.info(
            f"Sending post booking message with flight_booked: {flight_booked}, hotel_booked: {hotel_booked}",
            flight_mask,
        )
        response = await b.ConversePostBookingLight(
            flight_booked=flight_booked,
            hotel_booked=hotel_booked,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
        new_message = AIMessage(content=response)

        await message_persistor([new_message])
        message = await map_websocket_message(
            new_message,
            None,
            None,
        )
        await self.message_sender(message={**message[0]})

    async def record_search_trigger(self, search_type: SearchType):
        updated_preferences = {}
        activity_type = None

        if search_type == SearchType.FLIGHT:
            updated_preferences = await increment_triggered_search_amount(self.user.id, search_type=SearchType.FLIGHT)
            activity_type = "flight_search"
        elif search_type == SearchType.HOTEL:
            updated_preferences = await increment_triggered_search_amount(self.user.id, search_type=SearchType.HOTEL)
            activity_type = "hotel_search"

        if activity_type:
            asyncio.create_task(
                update_user_activity(str(self.user.id), activity_type),
                name=f"update_user_activity_{activity_type}",
            )

        if updated_preferences:
            total_triggered_search_amount = (
                updated_preferences.triggered_flight_search_amount + updated_preferences.triggered_hotel_search_amount
            )
            if total_triggered_search_amount == settings.HIDE_SAMPLES_AFTER_N_SEARCHES:
                await self.message_sender(
                    message={
                        "type": "profile_update",
                        "expectResponse": True,
                    }
                )
