import asyncio
import json
import urllib.parse
from typing import Any, Callable, Coroutine, Dict, List, Optional, Tuple

from fastapi.exceptions import HTTPException
from langchain_core.messages import AIMessage, BaseMessage

import front_of_house_agent.back_of_house_executor.flight_and_hotel_executor as fhe
from baml_client import b
from baml_client.types import (
    HotelBookingItem,
    HotelPlanningStep,
    HotelSearchCoreCriteria,
    HotelSegment,
    HotelSelectResult,
    HotelValidationItem,
)
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.utils import build_open_hotels_payment_form_message
from front_of_house_agent.common_models import (
    HotelValidationResult,
)
from hotel_agent.booking_dot_com_models import BookingStatus
from hotel_agent.booking_dot_com_tools import (
    HotelBookingPaymentTiming,
    booking_failed_status,
    client_side_parameter_error_status_codes,
)
from llm_utils.llm_utils import is_valid_future_date
from server.database.models.bookings import Booking
from server.database.models.user import User, UserRole
from server.services.memory.trips.memory_modules.bookings_memory import BookingOperation, BookingsMemory
from server.services.trips.bookings import construct_accommodation_dict, construct_hotel_room_dict
from server.utils.booking_utils import extract_booking_dates_and_status
from server.utils.logger import logger
from server.utils.remote_cache import RemoteCache
from server.utils.settings import AgentTypes, settings
from server.utils.smtp import send_booking_email
from virtual_travel_agent.helpers import console_masks, get_current_date_string

hotel_mask = console_masks["hotel"]


async def validate_hotel_search(fh_executor: "fhe.TripPlanExecutor", hotel_search_core: HotelSearchCoreCriteria):
    validate_list = []

    if not hotel_search_core.check_in_date:
        validate_list.append("check in date is missing")
    elif not is_valid_future_date(hotel_search_core.check_in_date, fh_executor.timezone):
        validate_list.append(
            f"A past date {hotel_search_core.check_in_date} is provided for the check in date. I won't be able to search for past hotels."
        )

    if not hotel_search_core.check_out_date:
        validate_list.append("check out date is missing")
    elif not is_valid_future_date(hotel_search_core.check_out_date, fh_executor.timezone):
        validate_list.append(
            f"A past date {hotel_search_core.check_out_date} is provided for the check out date. I won't be able to search for past hotels."
        )

    if validate_list:
        combined_response = await b.RephraseRequestForMissingOrInvalidInfo(
            sentences=[
                "Looks like there're something to clarify about your hotel before I could proceed.",
                *validate_list,
            ],
            current_date=get_current_date_string(fh_executor.timezone),
            user_name=fh_executor.user.name,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            need_response_with_name=False,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
        return AIMessage(
            content=combined_response.combinedText,
            additional_kwargs={
                "agent_classification": AgentTypes.HOTELS,
            },
        )
    return None


async def search_hotels(
    fh_executor: "fhe.TripPlanExecutor",
    hotel_select_result: HotelSelectResult,
    param_dict: dict[str, Any],
    messages: list[BaseMessage],
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    timezone: str | None,
):
    validation_message = await validate_hotel_search(fh_executor, HotelSearchCoreCriteria.model_validate(param_dict))
    if validation_message:
        await message_persistor([validation_message])
        message = await map_websocket_message(
            validation_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})
        return validation_message

    if hotel_select_result.hotel_name:
        expire_timestamp, rooms = _get_hotel_rooms(
            hotel_select_result.hotel_name,
            param_dict.get("check_in_date"),
            param_dict.get("check_out_date"),
            messages,
            timezone,
        )
        if rooms:
            response = AIMessage(
                content="Please select a hotel room",
                additional_kwargs={
                    "expire_timestamp": expire_timestamp,
                    "room_types": rooms,
                },
            )

            await message_persistor([response])
            response_message = await map_websocket_message(
                response,
                param_dict.get("check_in_date"),
                param_dict.get("check_out_date"),
            )
            await fh_executor.message_sender(message={**response_message[0]})
            return response

    fh_executor.schedule_send_message(
        message={
            "type": "hotels_skeleton_async",
            "text": "Searching for available hotels now. I'll come back shortly with the best options for you.",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )
    logger.info(f"Searching hotels with criteria: {json.dumps(param_dict)}", hotel_mask)
    response = await fh_executor.hotel_helper.hotels_search(param_dict, fh_executor.schedule_send_message)

    await message_persistor([response])

    response_message = await map_websocket_message(
        response,
        param_dict.get("check_in_date"),
        param_dict.get("check_out_date"),
    )
    await fh_executor.message_sender(message={**response_message[0]})
    await _prompt_to_update_preferences(param_dict, fh_executor, message_persistor)
    return response


async def search_equivalent_hotels(
    fh_executor: "fhe.TripPlanExecutor",
    hotel_select_result: HotelSelectResult,
    param_dict: dict[str, Any],
    message_strs: list[str],
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
):
    validation_message = await validate_hotel_search(fh_executor, HotelSearchCoreCriteria.model_validate(param_dict))
    if validation_message:
        await message_persistor([validation_message])
        message = await map_websocket_message(
            validation_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})
        return validation_message

    logger.info(f"Searching hotels with criteria: {json.dumps(param_dict)}", hotel_mask)
    updated_param_dict = param_dict.copy()
    updated_param_dict.update(
        {
            "messages": message_strs,
        }
    )
    response = await fh_executor.hotel_helper.equivalent_hotels_search(
        updated_param_dict, hotel_select_result, fh_executor.schedule_send_message
    )

    await message_persistor([response])

    response_message = await map_websocket_message(
        response,
        param_dict.get("check_in_date"),
        param_dict.get("check_out_date"),
    )
    await fh_executor.message_sender(message={**response_message[0]})
    return response


async def validate_hotel(
    fh_executor: "fhe.TripPlanExecutor",
    hotel_select_result: HotelSelectResult,
    hotel_search_criteria_core: HotelSearchCoreCriteria,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    on_hotel_validated: Callable | None,
    hotel_segments: list[HotelSegment] | None,
    selected_hotel_for_segment: dict[str, HotelSelectResult] | None,
    message_strs: list[str] = [],
):
    fh_executor.schedule_send_message(
        message={
            "type": "search_update",
            "text": "I'm confirming your hotel selection. Please hold a moment.",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )
    logger.info(f"Validating hotel with criteria: {hotel_select_result.model_dump()}", hotel_mask)
    response = None
    try:
        company_admin_user: User | None = None
        if fh_executor.user.organization_id:
            company_admin_user = await User.from_organization_id_and_role(
                fh_executor.user.organization_id, UserRole.company_admin
            )

        _, _, display_payment_profile_form = await fh_executor.booking_dot_com_tools.check_payment_profile(
            fh_executor.user, company_admin_user
        )

        if display_payment_profile_form:
            new_message = AIMessage(
                content="I don't have your payment information on file. Please provide these details now so I can reserve your hotel for you.",
            )
            new_message.additional_kwargs = {
                "agent_classification": AgentTypes.HOTELS,
            }

            await message_persistor([new_message])
            message = await map_websocket_message(
                new_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**message[0]})
            await fh_executor.message_sender(message=build_open_hotels_payment_form_message())
            return new_message

        if hotel_segments and selected_hotel_for_segment:
            tasks = []
            for index, selected_hotel in selected_hotel_for_segment.items():
                segment = hotel_segments[int(index)]

                async def preview_task_wrapper(index, selected_hotel: HotelSelectResult, core: HotelSegment):
                    return (
                        index,
                        await fh_executor.booking_dot_com_tools.do_order_preview(
                            selected_hotel.room_product_id,
                            selected_hotel.property_id,
                            core.check_in_date,
                            core.check_out_date,
                        ),
                    )

                tasks.append(asyncio.create_task(preview_task_wrapper(index, selected_hotel, segment)))
            preview_payloads = await asyncio.gather(*tasks)
            if not all(preview_payloads):
                raise ValueError(
                    "I have problem getting your hotel order preview for you to review. Ask me to search again."
                )

            hotel_validation_results = {}
            hotel_validaiton_items = []
            for index, preview_payload in preview_payloads:
                if preview_payload is None or "data" not in preview_payload:
                    raise ValueError(
                        "I have problem getting your hotel order preview for you to review. Ask me to search again."
                    )

                data = preview_payload["data"]
                order_token = data.get("order_token", "0")
                hotel_validation_results[index] = HotelValidationResult(
                    order_token=order_token,
                    validated_price=(((data.get("accommodation") or {}).get("price") or {}).get("total") or {}).get(
                        "booker_currency"
                    ),
                )
                selected_hotel = selected_hotel_for_segment.get(str(index))
                if selected_hotel is not None:
                    data["hotel_name"] = selected_hotel.hotel_name
                    data["room_title"] = selected_hotel.room_title
                preview_payload_str = json.dumps(data)
                hotel_validaiton_items.append(
                    HotelValidationItem(
                        preview_order_data=preview_payload_str,
                        payment_timing=selected_hotel.payment_timing if selected_hotel else None,
                        cancellation_type=selected_hotel.cancellation_type if selected_hotel else None,
                    )
                )
            validation_response = await b.ConverseHotelValidations(
                items=hotel_validaiton_items,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            logger.log_baml()

            if hotel_validation_results and on_hotel_validated:
                await on_hotel_validated(None, None, hotel_validation_results)

            new_message = AIMessage(content=validation_response)
            new_message.additional_kwargs = {
                "agent_classification": AgentTypes.HOTELS,
            }

            await message_persistor([new_message])
            message = await map_websocket_message(
                new_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**message[0]})
            return new_message

        else:
            preview_payload = await fh_executor.booking_dot_com_tools.do_order_preview(
                hotel_select_result.room_product_id,
                hotel_select_result.property_id,
                hotel_search_criteria_core.check_in_date,
                hotel_search_criteria_core.check_out_date,
            )
            if preview_payload is None or "data" not in preview_payload:
                raise ValueError(
                    "I have problem getting your hotel order preview for you to review. Ask me to search again."
                )

            data = preview_payload["data"]
            order_token = data.get("order_token", "0")  # we need this for booking

            data["hotel_name"] = hotel_select_result.hotel_name
            data["room_title"] = hotel_select_result.room_title

            preview_payload_str = json.dumps(data)

            payment_timing = hotel_select_result.payment_timing

            response = await b.ConverseHotelValidation(
                preview_order_data=preview_payload_str,
                travel_context=hotel_search_criteria_core.model_dump_json(exclude_none=True) or "",
                payment_timing=payment_timing,
                cancellation_type=hotel_select_result.cancellation_type,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
                messages=None,
            )
            logger.log_baml()
            if order_token and on_hotel_validated and response.validated_price:
                await on_hotel_validated(order_token, response.validated_price, None)

            new_message = AIMessage(content="")
            json_representation = response.model_dump_json()
            new_message.additional_kwargs = {
                "function_call": {
                    "arguments": json_representation,
                    "name": "HotelValidationResponse",
                },
                "agent_classification": AgentTypes.HOTELS,
                "step": HotelPlanningStep.HOTEL_VALIDATION,
            }

            await message_persistor([new_message])
            message = await map_websocket_message(
                new_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**message[0]})
            return new_message

    except HTTPException as http_e:
        if http_e.status_code in client_side_parameter_error_status_codes:
            logger.info(
                f"Hotel preview failed with {http_e.status_code} error (client-side parameter error), searching for alternative hotels",
                hotel_mask,
            )
            try:
                search_params = _convert_hotel_criteria_to_search_params(hotel_search_criteria_core)
                fh_executor.schedule_send_message(
                    message={
                        "type": "hotels_skeleton_async",
                        "text": "Sorry about this, but the selected hotel isn't available for your dates. I'm running a quick search for similar options.",
                        "isBotMessage": True,
                        "expectResponse": False,
                    }
                )
                return await search_equivalent_hotels(
                    fh_executor,
                    hotel_select_result,
                    search_params,
                    message_strs,
                    message_persistor,
                )
            except Exception as search_error:
                logger.error(f"Failed to find alternative hotels: {str(search_error)}", hotel_mask)
                raise search_error

        raise http_e
    except Exception as e:
        logger.error(f"Error validating hotel: {str(e)}", hotel_mask)
        raise e


def _convert_hotel_criteria_to_search_params(hotel_search_criteria_core: HotelSearchCoreCriteria) -> dict[str, Any]:
    """Convert HotelSearchCoreCriteria to the dict format expected by search_hotels"""
    return {
        "check_in_date": hotel_search_criteria_core.check_in_date,
        "check_out_date": hotel_search_criteria_core.check_out_date,
        "city": hotel_search_criteria_core.city,
        "location_latitude_longitude": hotel_search_criteria_core.location_latitude_longitude,
        "hotel_search_radius": hotel_search_criteria_core.hotel_search_radius,
        "travel_context": hotel_search_criteria_core.model_dump_json(exclude_none=True) or "",
        "messages": [],
        "preferred_payment_timings": [],
        "min_price": None,
        "max_price": None,
        "is_mobile": False,
    }


async def is_hotel_ready_to_book(
    hotel_segments: list[HotelSegment] | None,
    hotel_select_result: HotelSelectResult,
    hotel_validation_result: HotelValidationResult,
    hotel_validation_result_for_segment: dict[str, HotelValidationResult] | None,
    hotel_select_result_for_segment: dict[str, HotelSelectResult] | None,
):
    if hotel_segments and hotel_validation_result_for_segment and hotel_select_result_for_segment:
        for index, validated_hotel in hotel_validation_result_for_segment.items():
            selected_hotel = hotel_select_result_for_segment[str(index)]
            if selected_hotel.room_product_id is None or validated_hotel.order_token is None:
                return False
    elif hotel_select_result.room_product_id is None or hotel_validation_result.order_token is None:
        return False

    return True


def _get_order_token_cache_key(user_id: int, thread_id: int, order_token: str):
    return f"{user_id}:{thread_id}:order_token:{order_token}"


async def _is_order_token_in_use(user_id: int, thread_id: int, order_token: str):
    """Check if order token is currently in use"""
    cache = RemoteCache()
    cache_key = _get_order_token_cache_key(user_id, thread_id, order_token)
    order_token_in_use, is_success = await cache.try_get(cache_key)
    return bool(order_token_in_use) and is_success


async def _mark_order_token_as_used(user_id: int, thread_id: int, order_token: str, expire_minutes: int = 15):
    """Mark order token as in use in cache with expiration"""
    cache = RemoteCache()
    cache_key = _get_order_token_cache_key(user_id, thread_id, order_token)
    from datetime import timedelta

    await cache.set(cache_key, "in_use", expire=timedelta(minutes=expire_minutes))


async def _release_order_token(user_id: int, thread_id: int, order_token: str) -> bool:
    """Release order token from cache"""
    cache = RemoteCache()
    cache_key = _get_order_token_cache_key(user_id, thread_id, order_token)
    return await cache.delete(cache_key)


async def _check_order_token_available(user_id: int, thread_id: int, order_token: str):
    """Check if order token is available and reserve it"""
    if await _is_order_token_in_use(user_id, thread_id, order_token):
        return False
    await _mark_order_token_as_used(user_id, thread_id, order_token)
    return True


async def hotel_booking(
    fh_executor: "fhe.TripPlanExecutor",
    check_in_date: str,
    check_out_date: str,
    city: str | None,
    hotel_select_result: HotelSelectResult,
    hotel_validation_result: HotelValidationResult,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    messages: list[BaseMessage],
    on_hotel_booked: Callable | None,
    hotel_segments: list[HotelSegment] | None,
    hotel_validation_result_for_segment: dict[str, HotelValidationResult] | None,
    hotel_select_result_for_segment: dict[str, HotelSelectResult] | None,
    selected_hotel_option_for_segment: Optional[Dict[str, Dict[str, Any]]],
):
    fh_executor.schedule_send_message(
        message={
            "type": "search_update",
            "text": "I'm waiting to get the hotel reservation confirmation. It shouldn't be too long.",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )
    if hotel_segments and hotel_validation_result_for_segment and hotel_select_result_for_segment:
        tasks = []
        for index, validated_hotel in hotel_validation_result_for_segment.items():
            selected_hotel = hotel_select_result_for_segment[str(index)]

            async def booking_task_wrapper(
                index, selected_hotel: HotelSelectResult, validated_hotel: HotelValidationResult
            ):
                assert selected_hotel.room_product_id is not None, "room_product_id can not be None during booking"
                assert validated_hotel.order_token is not None, "order_token can not be None during booking"

                # Check if order token is already in use
                if not await _check_order_token_available(
                    fh_executor.user.id, fh_executor.thread.id, validated_hotel.order_token
                ):
                    logger.warning(
                        f"Order token {validated_hotel.order_token} is already in use for segment {index}, skipping booking"
                    )
                    return (index, "Order token is already in use, booking skipped")

                try:
                    result = await fh_executor.hotel_helper.hotel_booking(
                        {
                            "room_product_id": selected_hotel.room_product_id or "",
                            "order_token": validated_hotel.order_token or "",
                            "payment_timing": HotelBookingPaymentTiming.from_baml_payment_timing(
                                selected_hotel.payment_timing
                            ),
                        }
                    )
                    return (index, result)
                except Exception as e:
                    # Release token on error
                    await _release_order_token(fh_executor.user.id, fh_executor.thread.id, validated_hotel.order_token)
                    logger.error(f"Hotel booking failed for segment {index}: {e}")
                    return (index, f"Booking failed: {str(e)}")

            tasks.append(asyncio.create_task(booking_task_wrapper(index, selected_hotel, validated_hotel)))

        booking_payloads = await asyncio.gather(*tasks)

        booking_items = []

        for index, booking_payload in booking_payloads:
            segment_booking_info = {}

            hotel_selection_result = hotel_select_result_for_segment.get(str(index))
            assert hotel_selection_result is not None, "hotel_selection_result can not be None during booking"

            segment_hotel_selection = hotel_select_result_for_segment.get(str(index))
            segment_hotel_core = hotel_segments[int(index)]

            if isinstance(booking_payload, str):
                segment_booking_info = {
                    "status": "failed",
                    "error": booking_payload,
                }
            elif isinstance(booking_payload, dict):
                if booking_payload.get("status") == booking_failed_status:
                    segment_booking_info = {"status": "failed", "error": booking_payload.get("error_response", "")}
                else:
                    data = booking_payload.get("data")

                    if data:
                        segment_booking_info = {
                            "status": "success",
                            "data": booking_payload,
                        }
                        booked_card_ids = {
                            "accomodation_reservation_number": (data.get("accommodation") or {}).get("reservation"),
                            "accomodation_order_number": (data.get("accommodation") or {}).get("order"),
                            "accomodation_pincode_number": (data.get("accommodation") or {}).get("pincode"),
                            "property_id": segment_hotel_selection.property_id if segment_hotel_selection else "",
                            "room_product_id": segment_hotel_selection.room_product_id
                            if segment_hotel_selection
                            else "",
                            "payment_timing": segment_hotel_selection.payment_timing
                            if segment_hotel_selection
                            else None,
                        }

                        booking_details = await _save_hotel_booking(
                            fh_executor.thread.id,
                            fh_executor.user.id,
                            booked_card_ids,
                            segment_hotel_core.check_in_date,
                            segment_hotel_core.check_out_date,
                            segment_hotel_core.city,
                            messages,
                            fh_executor.user.email,
                            selected_hotel_option_for_segment,
                        )

                        asyncio.create_task(
                            BookingsMemory(
                                user_id=str(fh_executor.user.id), thread_id=str(fh_executor.thread.id)
                            ).store_hotel_booking_memory(
                                booking_details=booking_details,
                                operation=BookingOperation.BOOKED,
                            )
                        )
                    else:
                        segment_booking_info = {
                            "status": "failed",
                            "error": "Booking payload does not contain data",
                        }

            booking_items.append(
                HotelBookingItem(
                    segment_info=hotel_segments[int(index)],
                    segment_booking_info=json.dumps(segment_booking_info),
                    hotel_selection=hotel_selection_result,
                )
            )

        booking_response = await b.ConverseHotelBookings(
            items=booking_items,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()

        new_message = AIMessage(content=booking_response)
        new_message.additional_kwargs = {
            "agent_classification": AgentTypes.HOTELS,
        }

        await message_persistor([new_message])
        message = await map_websocket_message(
            new_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**message[0]})

        if on_hotel_booked:
            await on_hotel_booked()

        await fh_executor.send_post_booking_message(message_persistor)

        return new_message

    assert hotel_validation_result.order_token is not None, "when booking, the order token can not be None"

    # Check if order token is already in use
    if not await _check_order_token_available(
        fh_executor.user.id, fh_executor.thread.id, hotel_validation_result.order_token
    ):
        logger.warning(f"Order token {hotel_validation_result.order_token} is already in use, skipping booking")
        return AIMessage(
            content="I'm sorry, but this hotel booking option is no longer available. Let me help you find another option.",
        )

    logger.info(f"Booking hotel with criteria: {hotel_validation_result}", hotel_mask)
    payment_timing = hotel_select_result.payment_timing

    try:
        book_hotel_message = await fh_executor.hotel_helper.hotels_booking(
            {
                "room_product_id": hotel_select_result.room_product_id,
                "property_id": hotel_select_result.property_id,
                "check_in_date": check_in_date,
                "check_out_date": check_out_date,
                "order_token": hotel_validation_result.order_token,
                "validated_price": hotel_validation_result.validated_price,
                "hotel_name": hotel_select_result.hotel_name,
                "room_title": hotel_select_result.room_title,
                "payment_timing": HotelBookingPaymentTiming.from_baml_payment_timing(payment_timing),
            }
        )
        response_data = book_hotel_message.additional_kwargs["function_call"]

        if response_data.get("status") == booking_failed_status:
            # Release token since booking failed
            await _release_order_token(fh_executor.user.id, fh_executor.thread.id, hotel_validation_result.order_token)
            await message_persistor([book_hotel_message])
            message = await map_websocket_message(
                book_hotel_message,
                None,
                None,
            )

            await fh_executor.message_sender(message={**message[0]})

            return book_hotel_message

        arguments_string = response_data["arguments"]
        logger.info(f"HotelBookingResponse: {arguments_string}", hotel_mask)
        arguments_obj = json.loads(arguments_string)

        booked_card_ids = {
            "hotel_selection": arguments_obj.get("hotel_selection"),
            "accomodation_reservation_number": arguments_obj.get("accomodation_reservation_number"),
            "accomodation_order_number": arguments_obj.get("accomodation_order_number"),
            "accomodation_pincode_number": arguments_obj.get("accomodation_pincode_number"),
            "property_id": hotel_select_result.property_id,
            "room_product_id": hotel_select_result.room_product_id,
            "payment_timing": arguments_obj.get("payment_timing"),
        }

        booking_details = await _save_hotel_booking(
            fh_executor.thread.id,
            fh_executor.user.id,
            booked_card_ids,
            check_in_date,
            check_out_date,
            city,
            messages,
            fh_executor.user.email,
            selected_hotel_option_for_segment,
        )
        fh_executor.schedule_send_message(
            message={
                "type": "itinerary_update",
            }
        )
        fh_executor.schedule_send_message(
            message={
                "type": "trip_update",
            }
        )

        asyncio.create_task(
            BookingsMemory(
                user_id=str(fh_executor.user.id), thread_id=str(fh_executor.thread.id)
            ).store_hotel_booking_memory(
                booking_details=booking_details,
                operation=BookingOperation.BOOKED,
            )
        )

        await message_persistor([book_hotel_message])
        message = await map_websocket_message(
            book_hotel_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**message[0]})

        if on_hotel_booked:
            await on_hotel_booked()

        await fh_executor.send_post_booking_message(message_persistor)

        return book_hotel_message

    except Exception as e:
        # Release token on any error
        await _release_order_token(fh_executor.user.id, fh_executor.thread.id, hotel_validation_result.order_token)
        logger.error(f"Hotel booking failed: {e}")
        raise


async def _save_hotel_booking(
    thread_id: int,
    user_id: int,
    booked_card_ids: dict[str, str],
    checkin_date: str,
    checkout_date: str,
    city: str | None,
    messages: List[BaseMessage],
    email: str,
    selected_hotel_option_for_segment: Optional[Dict[str, Dict[str, Any]]] = None,
):
    query: dict[str, Any] = {"thread_id": thread_id, "user_id": user_id, "type": "accommodations"}
    selected_hotel_room: dict[str, Any] = {
        "content": {},
    }

    manage_booking_url = None
    accommodation_raw = None

    if selected_hotel_option_for_segment:
        property_id = booked_card_ids.get("property_id")
        if property_id:
            for segment_hotel_options in selected_hotel_option_for_segment.values():
                if segment_hotel_options.get("property_id") == property_id:
                    accommodation_raw = segment_hotel_options
                    break

    if not accommodation_raw:
        for message in messages:
            try:
                if "function_call" not in message.additional_kwargs:
                    continue

                message_dict = {}
                if isinstance(message.additional_kwargs["function_call"]["arguments"], dict):
                    message_dict = message.additional_kwargs["function_call"]["arguments"]
                else:
                    message_dict = json.loads(message.additional_kwargs["function_call"]["arguments"])

                if "property_id" in booked_card_ids.keys() and "hotel_options" in message_dict.keys():
                    accommodation_raws = [
                        x
                        for x in message_dict["hotel_options"]
                        if x.get("property_id") == booked_card_ids["property_id"]
                    ]
                    if len(accommodation_raws) > 0:
                        accommodation_raw = accommodation_raws[0]
                        break
            except ValueError:
                pass
            except Exception as e:
                logger.error(f"Failed to parse message during save booking: {e}")

    if accommodation_raw:
        booked_accommodation = await construct_accommodation_dict(accommodation_raw, checkin_date, checkout_date)

        room_id = int(booked_card_ids.get("room_product_id", "").split("_")[0])
        booked_accommodation["room"] = [
            room for room in booked_accommodation.get("rooms", [{}]) if room.get("id") == room_id
        ][0]
        del booked_accommodation["rooms"]

        website = accommodation_raw.get("dropoff_url")

        assert settings.BOOKING_DOT_COM_AFFILIATE_ID is not None, "BOOKING_DOT_COM_AFFILIATE_ID is not set"
        # Admin url: https://secure.booking.com/mybooking.en-us.html?aid=<affiliated_id>&bn=<order_number>&pincode=<pincode_number>
        manage_booking_url_parsed = urllib.parse.urlsplit("https://secure.booking.com/mybooking.en-us.html")
        manage_booking_url_query_params = dict(urllib.parse.parse_qsl(manage_booking_url_parsed.query))
        manage_booking_url_query_params["aid"] = settings.BOOKING_DOT_COM_AFFILIATE_ID
        manage_booking_url_query_params["bn"] = booked_card_ids.get("accomodation_order_number", "")
        manage_booking_url_query_params["pincode"] = booked_card_ids.get("accomodation_pincode_number", "")

        manage_booking_url_query = urllib.parse.urlencode(manage_booking_url_query_params)
        manage_booking_url = urllib.parse.urlunsplit(
            (
                manage_booking_url_parsed.scheme,
                manage_booking_url_parsed.netloc,
                manage_booking_url_parsed.path,
                manage_booking_url_query,
                manage_booking_url_parsed.fragment,
            )
        )
        booked_accommodation["website"] = website
        booked_accommodation["manageBookingURL"] = manage_booking_url
        booked_accommodation["check_in_date"] = checkin_date
        booked_accommodation["check_out_date"] = checkout_date

        selected_hotel_room["content"] = booked_accommodation

        selected_hotel_room["content"]["reservation_number"] = booked_card_ids.get("accomodation_reservation_number")
        selected_hotel_room["content"]["order_number"] = booked_card_ids.get("accomodation_order_number")
        selected_hotel_room["content"]["pincode_number"] = booked_card_ids.get("accomodation_pincode_number")
        selected_hotel_room["content"]["property_id"] = booked_card_ids.get("property_id")
        selected_hotel_room["content"]["status"] = BookingStatus.BOOKED.value.lower()
        selected_hotel_room["content"]["payment_timing"] = booked_card_ids.get("payment_timing")
        selected_hotel_room["content"]["city"] = city

    if not selected_hotel_room["content"]:
        logger.error(
            f"No selected hotel room found for property_id: {booked_card_ids['property_id']}, booked_card payload: {booked_card_ids}"
        )

    start_date, end_date, status = extract_booking_dates_and_status(
        "accommodations", selected_hotel_room.get("content", {})
    )

    if start_date:
        selected_hotel_room["start_date"] = start_date
    if end_date:
        selected_hotel_room["end_date"] = end_date
    if status:
        selected_hotel_room["status"] = status.name

    new_booking: Booking = Booking(**{**query, **selected_hotel_room})
    await Booking.new_booking(new_booking)

    await send_booking_email(
        "hotel",
        email,
        thread_id,
        extra_info=f"booking_dot_com_id={selected_hotel_room['content']['order_number']}\n"
        f"booking_dot_com_pincode={selected_hotel_room['content']['pincode_number']}\n"
        f"booking_dot_com_reservation_number={selected_hotel_room['content']['reservation_number']}\n"
        f"booking_dot_com_property_id={selected_hotel_room['content']['property_id']}\n"
        f"booking_dot_com_admin_url={manage_booking_url}",
    )
    return new_booking


def _get_hotel_rooms(
    property_name: str | None,
    check_in_date: str | None,
    check_out_date: str | None,
    messages: List[BaseMessage],
    timezone: str | None,
) -> Tuple[str | None, List[dict[str, Any]]]:
    if property_name:
        for message in messages[::-1]:
            if "hotel_options" in message.additional_kwargs.get("function_call", {}).get("arguments", ""):
                message_dict: dict[str, Any] = json.loads(message.additional_kwargs["function_call"]["arguments"])
                for hotel in message_dict["hotel_options"]:
                    assert isinstance(hotel, dict), f"hotel is not a dict: {hotel}"
                    if hotel.get("property_name") == property_name:
                        expire_timestamp: str | None = message.additional_kwargs.get("expire_timestamp")
                        rooms: List[dict[str, Any]] = [
                            construct_hotel_room_dict(
                                hotel.get("property_name"),
                                hotel.get("image_url"),
                                room,
                                check_in_date,
                                check_out_date,
                                timezone,
                            )
                            for room in hotel.get("rooms", [])
                        ]

                        return expire_timestamp, rooms
    return None, []


async def _prompt_to_update_preferences(
    param_dict: dict[str, Any],
    fh_executor: "fhe.TripPlanExecutor",
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
):
    response = await b.PromptUserToUpdatePreferencesAfterSearch(
        "hotel", search_criteria=str(param_dict), preferences=None
    )
    if response:
        msg = AIMessage(
            content=response,
            additional_kwargs={"agent_classification": AgentTypes.PREFERENCES},
        )
        await message_persistor([msg])

        messages = await map_websocket_message(msg)

        await fh_executor.message_sender(message={**messages[0]})
