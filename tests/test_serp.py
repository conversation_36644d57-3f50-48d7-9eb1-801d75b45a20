import json
from datetime import datetime

import pytest

from front_of_house_agent.serp_common_models import <PERSON>p<PERSON><PERSON><PERSON>ars<PERSON>
from front_of_house_agent.serp_flight_helper import <PERSON><PERSON><PERSON><PERSON>SearchHelper
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User

test_user = User(
    id=1,
    email="test",
    profile_picture="test.jpg",
    first_name="test",
    last_name="test",
    preferred_name="test",
    created_date=datetime.fromisoformat("2023-01-01"),
    tutorial_completed=True,
    citizenship=["USA"],
)


def test_process_flight_search_results():
    json_response = {}
    # Read JSON response from file
    with open("./tests/data/serp_test_booking_response.json", "r") as file:
        json_response = file.read()

    json_response_list = json.loads(json_response)
    result = SerpFlightParser.parse_booking_option_response(json_response_list[1])
    # TODO: consider to remove this line or fix it
    flightSerpHelper = SerpFlightSearchHelper(None, None, None)  # type: ignore
    result = flightSerpHelper.process_flight_search_results([result])
    assert result is not None
    assert len(result[1]) > 0
    for res in result[1]:
        assert res.model_dump_json(indent=4) is not None


@pytest.mark.asyncio
async def test_search_flights_success(mocker):
    mock_response = {"flights": "mocked_response"}
    mock_google_serp_api = mocker.patch(
        "front_of_house_agent.serp_flight_helper.google_serp_api.search_flights", return_value=mock_response
    )
    params = {"departure_id": "SEA", "arrival_id": "SFO", "outbound_date": "2023-10-10"}

    flightSerpHelper = SerpFlightSearchHelper(test_user, ChatThread(1, "test"), None)
    result = await flightSerpHelper.__search_flights(params)

    mock_google_serp_api.assert_called_once_with(params)
    assert result == mock_response


@pytest.mark.asyncio
async def test_search_flights_general_exception(mocker):
    mocker.patch(
        "front_of_house_agent.serp_flight_helper.google_serp_api.search_flights", side_effect=Exception("General error")
    )
    params = {"departure_id": "SEA", "arrival_id": "SFO", "outbound_date": "2023-10-10"}
    flightSerpHelper = SerpFlightSearchHelper(test_user, ChatThread(1, "test"), None)
    with pytest.raises(Exception, match="General error"):
        await flightSerpHelper.__search_flights(params)
