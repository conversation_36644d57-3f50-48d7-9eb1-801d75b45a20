# Otto - A multi-agent travel planning application 🚀

A travel planning chat application powered by a multi-agent LLM framework

This repo contains the backend of the app

### Do this to run the app:

1) `brew install just`

2) run `just install-uv`, and `just dev-setup`. you can activate the venv with `source .venv/bin/activate`

3) Run command: `just baml-generate`

4) Create local DNS:
    - open in a text editor `/etc/hosts` (mac + linux) / `C:\Windows\System32\drivers\etc\hosts` (win) - you will most likely need to open it with sudo / administrator rights
    - Add the following at the end of the file: `127.0.0.1   api.local.dev.otto-demo.com`

5) Generate ssl certificates:
    - Install mkcert: `brew install mkcert` (see more options to install mkcert on https://github.com/FiloSottile/mkcert)
    - Open terminal at project root
    - Run `mkcert -install`
    - Run `mkdir certificates && cd certificates`
    - Run `mkcert api.local.dev.otto-demo.com`

6) To run the server: `just dev-start`
7) Run `pre-commit install` for linting check before git commit.
8) Adding package will be `uv add (--dev) package-name`
9) If you are using vs-code, make sure to add `ruff` extension, i can do lint and format.

Access the appplication: https://api.local.dev.otto-demo.com:8000/

### Secrets / Environment variables

The secrets for OTTO_ENV=DEV environment are stored on AWS (us-east-2) -> Secrets Manager -> otto-server-dev.
For the app to access the Secrets Manager, the following environment variables keys have to be set:

    OTTO_ENV=DEV
    AWS_ACCESS_KEY_ID = your_access_key_here
    AWS_SECRET_ACCESS_KEY = your_secret_access_key_here
    AWS_DEFAULT_REGION = us-east-2

By default the env variables from Secrets Manager will be used, but they can be overwritten from the .env file. For a complete list of the keys that can be overwritten, see .env.example file.

To use a different PostgreSQL database, first update the env variables for PG and then run the following command to create / update the database: `alembic upgrade head`.

In order to run locally, create a file named `.env` and put your env variables. can ask other team member for complete env variables.

### Running tests
Right now the tests are being actively constructed and you should add your test cases as well. We're using pytest + asyncio to run agent tests.

You could use visual studio code "Testing" tab to run tests, which is recommended.

Alternatively, here's the command line to run one of the test. Note that `pytest` command won't work for some reason.
```
uv run pytest -k "test_one_way_to_sfo_with_united_airline[FLIGHT_BOOKING" --junitxml=test-results/junit.xml
```
Omitting the `-k ...` parameter would run all tests, which is a lot.

### Alembic commands

1) `alembic revision --autogenerate -m "my message"` - this command will generate the sqlalchemy commands to create the new tables, new columns or drop tables / columns based on ORM models. All ORM models have to be imported into `server/database/migrations/env.py` file to be discovered automatically by alembic. After the command runs successfully, a new migration file will be generated in `server/database/migrations/versions/` folder. The new file will have to be checked manually to make sure the right commands were generated.
2) Before running migrations, configure your target environment by setting the `OTTO_ENV` variable in your `.env` file.
3) Then run `just alembic-upgrade-dryrun` to do a dry run, and `just alembic-upgrade` to do actual migration.

### Python lint & formatter

1. Make sure you installed latest `ruff` extension from VS Code extension hub
2. Create a settings.json file under .vscode (or cmd+shift+p and type "Open Workspace Settings" to open this json file) and put the following content in it:
```
{
    "python.testing.pytestArgs": [
        "virtual_travel_agent_tests"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    "editor.formatOnSave": true,
}
```

### Python Debugger
1. Go to vscode -> Run and Debug -> create a launch.json file, or manually create one in .vscode folder
2. Copy and paste below:
```
{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: FastAPI",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "cwd": "${workspaceFolder}",
            "args": [
                "server.main:app",
                "--port=8000",
                "--host=0.0.0.0",
                "--ssl-certfile=certificates/api.local.dev.otto-demo.com.pem",
                "--ssl-keyfile=certificates/api.local.dev.otto-demo.com-key.pem"
            ],
            "jinja": true,
            "justMyCode": false
        }
    ]
}
```

### Use Redis locally
Add `REDIS_URL="redis://localhost:6379"` to your `.env` file

#### Install redis locally
1. Install & Start colima and docker
```
brew install colima docker
colima start
```
2. Run redis 7.2 container and expose the port 6379 
```
docker run --name redis -d -p 6379:6379 redis:7.2
```
#### Or SSH tunnel to redis on aws

Note: 
- Check the `Connected compute resources` section in the redis console to make sure the EC2 instance is configured to access the redis instance.

An example cmd of how to create a ssh tunnel to the redis instance on dev
```
ssh -i ~/path/to/your/ec2.pem -N -L 6379:otto-redis-cluster-dev.vq0ce8.0001.use2.cache.amazonaws.com:6379 ubuntu@***********
```

### AWS Spotnana Proxy Configuration
#### Required for accessing Spotnana API from local development environment outside office network
1. Add `ENABLE_AWS_SPOTNANA_PROXY=true` to your `.env` file


### Mailgun Local Debugging
1. Update `MAILGUN_REPLYTO_EMAIL_PREFIX` to your test email prefix in `.env` file
2. Add new mailgun route and set its Expression to ```match_recipient("[Your MAILGUN_REPLYTO_EMAIL_PREFIX](\+.*)?@notify.ottotheagent.com")```
3. Use `ngrok http https://localhost:8000` if you want to expose public url for your local otto server. Especially useful for testing Mailgun webhook locally.

### Temporal Local
1. run `brew install temporal`
2. run `temporal server start-dev`
3. add `TEMPORAL_NS=default` and `TEMPORAL_HOST=localhost:7233` to your `.env` file.
4. go to `http://localhost:8233` to check temporal UI.
