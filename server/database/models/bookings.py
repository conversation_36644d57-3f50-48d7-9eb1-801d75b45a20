from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Literal, Sequence, Tuple, get_args

from sqlalchemy import TEXT, Enum, ForeignKey, Index, Row, cast, delete, func, select, text, update
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column

from server.database.models.chat_thread import ChatThread
from server.utils.pg_connector import Base, async_session

BookingType = Literal["accommodations", "flight"]


@dataclass
class BookingCnt:
    flight_cnt: int
    hotel_cnt: int


class Booking(Base):
    __tablename__ = "bookings"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    thread_id: Mapped[int] = mapped_column(ForeignKey(ChatThread.id))
    user_id: Mapped[int | None] = mapped_column(ForeignKey("users.id"), nullable=True)
    type: Mapped[BookingType] = mapped_column(
        Enum(
            *get_args(BookingType),
            name="booking_type",
            create_constraint=True,
            validate_strings=True,
        ),
        default=get_args(BookingType)[0],
        nullable=True,
    )
    content: Mapped[dict] = mapped_column(JSONB)
    flight_exchanges: Mapped[list[dict]] = mapped_column(JSONB, nullable=True)
    created_at: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)
    start_date: Mapped[datetime | None] = mapped_column(nullable=True)
    end_date: Mapped[datetime | None] = mapped_column(nullable=True)
    status: Mapped[str | None] = mapped_column(nullable=True)

    __table_args__ = (Index("ix_booking_content_gin", "content", postgresql_using="gin"),)

    @staticmethod
    def build_where_clauses_from_query(query_dict: dict[str, Any]):
        def parse_key(key):
            if "__" in key:
                base, op = key.rsplit("__", 1)
            else:
                base, op = key, "eq"
            return base, op

        where_clauses = []
        for key, value in query_dict.items():
            base_key, op = parse_key(key)

            if "." in base_key:
                # JSONB field
                col, json_key = base_key.split(".", 1)
                column = getattr(Booking, col)[json_key].astext
                if isinstance(value, list):
                    # If the value is a list, we need to cast it to TEXT[] for PostgreSQL
                    value_casted = [cast(str(v), TEXT) for v in value]
                else:
                    value_casted = cast(str(value), TEXT)
            else:
                # Normal column
                column = getattr(Booking, base_key)
                value_casted = value

            if op == "eq":
                clause = column.in_(value_casted) if isinstance(value_casted, (list, tuple)) else column == value_casted
            elif op == "ne":
                clause = (
                    column.nin_(value_casted) if isinstance(value_casted, (list, tuple)) else column != value_casted
                )
            elif op == "gt":
                clause = column > value_casted
            elif op == "lt":
                clause = column < value_casted
            elif op == "gte":
                clause = column >= value_casted
            elif op == "lte":
                clause = column <= value_casted
            else:
                raise ValueError(f"Unsupported operator {op} in key {key}.")

            where_clauses.append(clause)

        return where_clauses

    @staticmethod
    async def from_query(query_dict: dict[str, Any]) -> Booking | None:
        async with async_session() as session:
            async with session.begin():
                where_clauses = Booking.build_where_clauses_from_query(query_dict)
                query = select(Booking).where(*where_clauses)

                results: Row[Tuple[Booking]] | None = (await session.execute(query)).fetchone()

                if results is None:
                    return None

                return results[0]

    @staticmethod
    async def from_query_batch(
        query_dict: dict[str, Any], offset: int | None = None, limit: int | None = None
    ) -> list[Booking]:
        async with async_session() as session:
            async with session.begin():
                where_clauses = Booking.build_where_clauses_from_query(query_dict)
                query = select(Booking).where(*where_clauses).order_by(Booking.id)

                if offset:
                    query = query.offset(offset)

                if limit:
                    query = query.limit(limit)

                results: Sequence[Row[Tuple[Booking]]] = (await session.execute(query)).fetchall()
                return [res.Booking for res in results]

    @staticmethod
    async def new_booking(booking: Booking):
        async with async_session() as session:
            async with session.begin():
                session.add(booking)

    @staticmethod
    async def new_booking_batch(bookings: list[Booking]):
        async with async_session() as session:
            async with session.begin():
                session.add_all(bookings)

    @staticmethod
    async def update_fields(query_dict: dict[str, Any], update_dict: dict[str, Any]):
        # this function can not be used to update more than 2 fields in the json column.
        async with async_session() as session:
            async with session.begin():
                where_clauses = Booking.build_where_clauses_from_query(query_dict)

                column_updates = {}
                set_clauses = {}

                for key, value in update_dict.items():
                    if "." in key:
                        col, json_key = key.split(".", 1)
                        if col not in column_updates:
                            column_updates[col] = []
                        column_updates[col].append((json_key, value))
                    else:
                        set_clauses[key] = value

                for col, updates in column_updates.items():
                    expr = getattr(Booking, col)
                    for json_key, value in updates:
                        json_path = text(f"'{{{','.join(json_key.split('.'))}}}'")
                        expr = func.jsonb_set(expr, json_path, cast(value, JSONB), True)
                    set_clauses[col] = expr

                query = update(Booking).where(*where_clauses).values(**set_clauses)
                await session.execute(query)

    @staticmethod
    async def delete_batch(query_dict: dict[str, Any]):
        async with async_session() as session:
            async with session.begin():
                where_clauses = Booking.build_where_clauses_from_query(query_dict)

                query = delete(Booking).where(*where_clauses)
                await session.execute(query)

    @staticmethod
    async def from_query_count(query_dict: dict[str, Any]) -> int:
        async with async_session() as session:
            async with session.begin():
                where_clauses = Booking.build_where_clauses_from_query(query_dict)
                query = select(func.count()).select_from(Booking).where(*where_clauses)

                result = (await session.execute(query)).scalar()

                return result if result is not None else 0

    @staticmethod
    async def get_user_booking_counts(user_id: int) -> BookingCnt:
        """
        Get separate counts of flight and hotel bookings for a specific user.

        Args:
            user_id: The ID of the user to count bookings for

        Returns:
            Dictionary with 'flight' and 'hotel' keys containing respective counts
        """
        booking_types = get_args(BookingType)
        flight_type = next(t for t in booking_types if t == "flight")
        accommodations_type = next(t for t in booking_types if t == "accommodations")

        async with async_session() as session:
            async with session.begin():
                flight_query = (
                    select(func.count())
                    .select_from(Booking)
                    .where(Booking.user_id == user_id, Booking.type == flight_type)
                )

                hotel_query = (
                    select(func.count())
                    .select_from(Booking)
                    .where(Booking.user_id == user_id, Booking.type == accommodations_type)
                )

                flight_count = (await session.execute(flight_query)).scalar() or 0
                hotel_count = (await session.execute(hotel_query)).scalar() or 0

                return BookingCnt(flight_cnt=flight_count, hotel_cnt=hotel_count)
