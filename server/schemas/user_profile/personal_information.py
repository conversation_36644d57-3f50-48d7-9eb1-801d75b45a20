from typing import Literal, Optional

from pydantic import BaseModel


class PersonalInformationBaseRequest(BaseModel):
    first_name: str
    last_name: str
    phone: Optional[str] = None


class PersonalInformationExtendedRequest(PersonalInformationBaseRequest):
    title: Literal[
        "TITLE_UNKNOWN",
        "MR",
        "MS",
        "MRS",
        "MX",
        "MA<PERSON><PERSON>",
        "MISS",
        "DR",
        "PROFESSOR",
        "CAP<PERSON>IN",
        "<PERSON>E<PERSON><PERSON><PERSON>",
        "<PERSON>ON<PERSON><PERSON><PERSON><PERSON>",
        "SIR",
        "LADY",
        "AMBASSAD<PERSON>",
        "LORD",
        "BRIGADIER",
        "SENATOR",
        "DAME",
        "JUSTICE",
        "UK",
    ]
    dob: str
    gender: Literal["MALE", "FEMAL<PERSON>", "UNSPECIFIED", "UNDISCLOS<PERSON>"]
    traveler_number: Optional[str] = None
    redress_number: Optional[str] = None
    citizenship: Optional[list[str]] = None
