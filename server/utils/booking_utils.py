from datetime import date, datetime
from typing import Any, Dict, Optional, <PERSON><PERSON>

import dateutil.parser

from server.schemas.spotnana.flight_statuses import FlightStatuses
from server.utils.logger import logger


def extract_booking_dates_and_status(
    booking_type: str, content: Dict[str, Any]
) -> Tuple[Optional[date], Optional[date], Optional[FlightStatuses]]:
    """
    Extract start_date, end_date, and status from booking content.

    Args:
        booking_type: The type of booking ("accommodations" or "flight")
        content: The JSON content of the booking

    Returns:
        Tuple of (start_date, end_date, status)
    """
    status = content.get("status")
    if status == "cancelled":
        status = FlightStatuses.CANCELLED
    elif status == "pending":
        status = FlightStatuses.PENDING
    elif status == "booked":
        status = FlightStatuses.BOOKED
    elif status == "pending_cancel":
        status = FlightStatuses.PENDING_CANCEL
    else:
        logger.error(f"Unknown status: {status}")
    start_date = None
    end_date = None

    try:
        if booking_type == "accommodations":
            if check_in_date := content.get("check_in_date"):
                start_date = datetime.strptime(check_in_date, "%Y-%m-%d")

            if check_out_date := content.get("check_out_date"):
                end_date = datetime.strptime(check_out_date, "%Y-%m-%d")

        elif booking_type == "flight":
            if "legs" in content:
                legs = content.get("legs", [])
                if legs:
                    first_leg = legs[0]
                    flight_segments = first_leg.get("flight_segments", [])
                    if flight_segments and len(flight_segments) > 0:
                        flight_stops = flight_segments[0].get("flight_stops", [])
                        if flight_stops and flight_stops[0].get("departure"):
                            try:
                                start_date = dateutil.parser.isoparse(flight_stops[0].get("departure")).date()
                            except Exception as e:
                                logger.error(f"Error parsing flight departure date from legs: {e}")
                    if len(legs) > 1:
                        last_leg = legs[-1]
                        flight_segments = last_leg.get("flight_segments", [])
                        if flight_segments and len(flight_segments) > 0:
                            flight_stops = flight_segments[0].get("flight_stops", [])
                            if flight_stops and flight_stops[-1].get("departure"):
                                try:
                                    end_date = dateutil.parser.isoparse(flight_stops[-1].get("departure")).date()
                                except Exception as e:
                                    logger.error(f"Error parsing flight arrival date from legs: {e}")
            else:
                if content.get("outbound"):
                    outbound = content.get("outbound", {})
                    outbound_flight_segments = outbound.get("flight_segments", [])

                    if outbound_flight_segments and len(outbound_flight_segments) > 0:
                        outbound_flight_stops = outbound_flight_segments[0].get("flight_stops", [])

                        if outbound_flight_stops and outbound_flight_stops[0].get("departure"):
                            try:
                                start_date = dateutil.parser.isoparse(outbound_flight_stops[0].get("departure")).date()
                            except Exception as e:
                                logger.error(f"Error parsing outbound departure date: {e}")
                if content.get("return"):
                    return_one = content.get("return") or {}
                    return_flight_segments = return_one.get("flight_segments", [])

                    if return_flight_segments and len(return_flight_segments) > 0:
                        return_flight_stops = return_flight_segments[0].get("flight_stops", [])

                        if return_flight_stops and return_flight_stops[0].get("departure"):
                            try:
                                end_date = dateutil.parser.isoparse(return_flight_stops[0].get("departure")).date()
                            except Exception as e:
                                logger.error(f"Error parsing return departure date: {e}")

    except Exception as e:
        import traceback

        logger.error(traceback.format_exc())
        logger.error(f"Error extracting booking dates and status: {e}")

    return start_date, end_date, status
