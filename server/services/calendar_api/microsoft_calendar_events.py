from datetime import datetime, timed<PERSON><PERSON>
from functools import partial
from typing import Any, Dict, List

import httpx
import requests

from server.utils.logger import logger
from server.utils.settings import settings


class MicrosoftCalendarEvents:
    RELEVANT_FIELDS: List[str] = ["subject", "body", "location", "start", "end"]

    def __init__(self, refresh_token: str, user_email: str | None = None) -> None:
        """
        Initialize Microsoft Calendar Events handler

        :param refresh_token: Microsoft OAuth refresh token
        :param user_email: User's email address (optional)
        """
        self.refresh_token = refresh_token
        self.access_token = self._get_access_token_from_refresh_token()
        self.user_email = user_email
        self.graph_api_endpoint = "https://graph.microsoft.com/v1.0"

    async def revoke_access_token(self):
        try:
            # Microsoft doesn't have a direct token revocation endpoint like some providers
            # Instead, we need to use their logout endpoint
            params = {
                "client_id": settings.OUTLOOK_CLIENT_ID,
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://login.microsoftonline.com/common/oauth2/v2.0/logout", params=params
                )

                if response.status_code == 200:
                    return {"message": "Token successfully revoked", "status": True}

        except Exception as e:
            pass

    def _get_access_token_from_refresh_token(self) -> str:
        """
        Exchange refresh token for a new access token

        :return: New access token
        """

        try:
            token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
            payload = {
                "client_id": settings.OUTLOOK_CLIENT_ID,
                "client_secret": settings.OUTLOOK_CLIENT_SECRET,
                "refresh_token": self.refresh_token,
                "grant_type": "refresh_token",
                "scope": "https://graph.microsoft.com/.default",
            }
            response = requests.post(token_url, data=payload)
            response.raise_for_status()
            token_data = response.json()
            self.refresh_token = token_data.get("refresh_token", self.refresh_token)  # Update refresh token if provided
            return token_data["access_token"]
        except Exception as e:
            logger.error(f"Failed to refresh access token: {e}")
            raise

    def has_calendar_access(self) -> bool:
        """
        Check if the user has calendar access

        :return: Boolean indicating calendar access
        """
        try:
            response = requests.get(
                f"{self.graph_api_endpoint}/me/events?$top=1",
                headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
            )
            return response.status_code == 200
        except Exception:
            # logger.warning(f"Calendar access check failed: {e}")
            return False

    def get_calendars(self) -> List[Any]:
        calendars = []
        try:
            response = requests.get(
                f"{self.graph_api_endpoint}/me/calendars",
                headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
            )
            response.raise_for_status()
            calendars = response.json().get("value", [])
        except Exception as e:
            logger.error(f"Failed to retrieve calendars from Microsoft Calendar: {e}")
        return calendars

    def get_events(
        self,
        start_date: datetime,
        end_date: datetime,
        keywords: str = settings.GOOGLE_CALENDAR_EVENTS_KEYWORDS,
    ) -> List[Dict]:
        """
        Retrieve events within a specific date range

        :param start_date: Start of date range
        :param end_date: End of date range
        :param keywords: Comma-separated keywords to filter events
        :return: List of events
        """
        all_events = []
        calendars = self.get_calendars()
        for calendar in calendars:
            calendar_id = calendar["id"]
            logger.info(f"Retrieving events from calendar: {calendar['name']}")
            date_time_format = "%Y-%m-%dT%H:%M:%S.000Z"
            page_url = (
                f"{self.graph_api_endpoint}/me/calendars/{calendar_id}/events"
                f"?$filter=start/dateTime ge '{start_date.strftime(date_time_format)}'"
                f" and end/dateTime le '{end_date.strftime(date_time_format)}'"
                "&$orderby=start/dateTime"
                "&$top=300"
            )

            try:
                while page_url:
                    response = requests.get(
                        page_url,
                        headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
                    )
                    response.raise_for_status()
                    page_data = response.json()

                    all_events.extend(page_data.get("value", []))
                    logger.info(f"Retrieved {len(all_events)} events so far...")

                    # Check for pagination
                    page_url = page_data.get("@odata.nextLink")
            except Exception as e:
                logger.error(f"Failed to retrieve events from Microsoft Calendar: {e}")
                continue

        # Filter by keywords if provided
        if len(all_events) > 0 and keywords and len(keywords) > 0:
            keywords_arr = [x.strip() for x in keywords.split(",")]
            all_events = list(filter(partial(self.filter_events, keywords=keywords_arr), all_events))

        logger.info(f"Total {len(all_events)} events that's travel related.")
        return all_events

    def get_newest_events(
        self,
        time_min: datetime,
        max_results: int = 10,
        keywords: str = settings.GOOGLE_CALENDAR_EVENTS_KEYWORDS,
    ) -> List[Dict]:
        """
        Retrieve the newest events since a given time

        :param time_min: Minimum datetime to retrieve events from
        :param max_results: Maximum number of events to retrieve
        :param keywords: Comma-separated keywords to filter events
        :return: List of newest events
        """

        date_time_format = "%Y-%m-%dT%H:%M:%S.000Z"
        response = requests.get(
            f"{self.graph_api_endpoint}/me/events"
            f"?$filter=start/dateTime ge '{time_min.strftime(date_time_format)}'"
            f"&$top={max_results}"
            "&$orderby=start/dateTime desc",
            headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
        )
        response.raise_for_status()
        events = response.json().get("value", [])

        # Filter by keywords if provided
        if keywords and len(keywords) > 0:
            keywords_arr = [x.strip() for x in keywords.split(",")]
            return list(filter(partial(self.filter_events, keywords=keywords_arr), events))

        return events

    def get_event_by_id(self, event_id: str, keywords: str = settings.GOOGLE_CALENDAR_EVENTS_KEYWORDS):
        """
        Retrieve a specific event by its ID.

        :param event_id: The ID of the event to retrieve
        :return: The event details as a dictionary
        """

        url = f"{self.graph_api_endpoint}/me/events/{event_id}"

        response = requests.get(
            url,
            headers={
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json",
            },
        )

        # Raise an error for non-2xx responses
        response.raise_for_status()

        events = response.json().get("value", [])

        # Filter by keywords if provided
        if keywords and len(keywords) > 0:
            keywords_arr = [x.strip() for x in keywords.split(",")]
            return list(filter(partial(self.filter_events, keywords=keywords_arr), events))

        # Return the event details
        return events

    def create_event(
        self,
        title: str,
        date_start: datetime,
        date_end: datetime,
        description: str = "",
        timezone_start: str | None = None,
        timezone_end: str | None = None,
        location: str | None = None,
    ) -> str | None:
        """
        Create a new calendar event

        :param title: Event title
        :param date_start: Start datetime of the event
        :param date_end: End datetime of the event
        :param description: Event description
        :param timezone_start: Start timezone
        :param timezone_end: End timezone
        :param location: Event location
        :return: Created event ID or None
        """
        datetime_format = "%Y-%m-%dT%H:%M:%S.000Z"
        event_body = {
            "subject": title,
            "start": {"dateTime": date_start.strftime(datetime_format), "timeZone": timezone_start or "UTC"},
            "end": {"dateTime": date_end.strftime(datetime_format), "timeZone": timezone_end or "UTC"},
            "isReminderOn": True,
        }

        if description:
            event_body["body"] = {"contentType": "HTML", "content": description}

        if location:
            event_body["location"] = {"displayName": location}

        try:
            response = requests.post(
                f"{self.graph_api_endpoint}/me/events",
                headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
                json=event_body,
            )
            response.raise_for_status()
            return response.json().get("id")
        except Exception as e:
            logger.error(f"Event creation failed: {e}")
            return None

    @staticmethod
    def filter_events(event: Dict, keywords: List[str]) -> bool:
        """
        Filter events based on keywords

        :param event: Event dictionary
        :param keywords: List of keywords to filter by
        :return: Boolean indicating if event matches keywords
        """
        text_fields = ["subject", "body", "location"]
        for k in keywords:
            for field in text_fields:
                # Check if keyword exists in the field (case-insensitive)
                if field in event and k.upper() in str(event[field]).upper():
                    return True
        return False

    @staticmethod
    def extract_relevant_fields(events: List[Dict]) -> List[Dict]:
        """
        Extract relevant fields from events

        :param events: List of event dictionaries
        :return: List of events with only relevant fields
        """
        return list(
            map(
                lambda x: {
                    "id": x.get("id", ""),
                    "summary": x.get("subject", ""),
                    "description": x.get("body", {}).get("content", ""),
                    "location": x.get("location", {}).get("displayName", ""),
                    "start": x.get("start", None),
                    "end": x.get("end", None),
                    "is_new": False,
                },
                events,
            )
        )

    def update_event(self, event_id: str, updates: Dict[str, Any]):
        """
        Update an existing event

        :param event_id: ID of the event to update
        :param updates: Dictionary of updates to apply
        """
        try:
            # Retrieve current event first
            current_event = requests.get(
                f"{self.graph_api_endpoint}/me/events/{event_id}",
                headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
            ).json()

            # Prepare update payload
            update_payload = {}

            # Handle special cases for different fields
            for k, v in updates.items():
                if k == "description":
                    # Append to existing description
                    current_desc = current_event.get("body", {}).get("content", "")
                    update_payload["body"] = {"contentType": "HTML", "content": f"{current_desc}\n{v}"}
                elif k in current_event:
                    update_payload[k] = v

            # Send update request
            response = requests.patch(
                f"{self.graph_api_endpoint}/me/events/{event_id}",
                headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
                json=update_payload,
            )
            response.raise_for_status()
        except Exception as e:
            logger.error(f"Event update failed: {e}")
            raise

    def watch_calendar(self) -> str | None:
        """
        Set up a webhook subscription for calendar events

        :return: Subscription ID or None
        """

        try:
            subscription_body = {
                "changeType": "created,updated,deleted",
                "notificationUrl": f"{settings.SERVER_DNS}/api/microsoft/calendar-webhook?shared_secret={self.refresh_token}&user_email={self.user_email}",
                "resource": f"users/{self.user_email}/events",
                "expirationDateTime": (datetime.utcnow() + timedelta(hours=24)).strftime("%Y-%m-%dT%H:%M:%S.000Z"),
            }

            response = requests.post(
                f"{self.graph_api_endpoint}/subscriptions",
                headers={"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"},
                json=subscription_body,
            )
            response.raise_for_status()
            return response.json().get("id")
        except Exception as e:
            logger.error(f"Calendar watch setup failed: {e}")
            return None

    def stop_watch(self, subscription_id: str):
        """
        Stop a calendar webhook subscription

        :param subscription_id: ID of the subscription to stop
        """
        try:
            response = requests.delete(
                f"{self.graph_api_endpoint}/subscriptions/{subscription_id}",
                headers={"Authorization": f"Bearer {self.access_token}"},
            )
            response.raise_for_status()
        except Exception as e:
            logger.warning(f"Stop watch failed: {e}")

    def search_event(self, query: str):
        # TODO Not implemented for microsoft.
        return []

    def delete_event(self, event_id: str):
        # TODO Not implemented for microsoft.
        pass

    def update_event_overwrite(
        self,
        event_id: str,
        title: str,
        date_start: datetime,
        date_end: datetime,
        description: str = "",
        timezone_start: str | None = None,
        timezone_end: str | None = None,
    ):
        logger.error("Microsoft calendar does not support event update overwrite.")
        # TODO Not implemented for microsoft.
        return {}
