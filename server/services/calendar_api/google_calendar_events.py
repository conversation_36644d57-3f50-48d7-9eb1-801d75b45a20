import hashlib
import j<PERSON>
import uuid
from datetime import datetime, timedelta
from functools import partial
from typing import Any, Dict, List

from fastapi import HTT<PERSON>Exception
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from requests import HTTPError

from server.services.calendar_api.google_api_base import GoogleApiBase
from server.utils.logger import logger
from server.utils.settings import settings


class GoogleCalendarEvents(GoogleApiBase):
    RELEVANT_FIELDS: List[str] = ["summary", "description", "location", "start", "end"]

    def __init__(self, refresh_token: str, user_email=None) -> None:
        super().__init__(refresh_token=refresh_token)

        self.calendar_id = "primary"
        self.user_email = user_email

    def has_calendar_access(self):
        try:
            self.authorize()
            service = build("calendar", "v3", credentials=self.creds, cache_discovery=False)
            service.events().list(calendarId=self.calendar_id, maxResults=1).execute()
            return True
        except Exception as e:
            logger.warning(f"Otto doesn't have access to the calendar of {self.user_email}. Error: {e}")
            return False

    def get_events(
        self,
        start_date: datetime,
        end_date: datetime,
        keywords: str = settings.GOOGLE_CALENDAR_EVENTS_KEYWORDS,
    ):
        self.authorize()
        service = build("calendar", "v3", credentials=self.creds)

        all_events = []

        try:
            events, next_page_token = self.get_page_events(service, start_date, end_date, page_token=None)
            all_events += events
            while next_page_token is not None:
                events, next_page_token = self.get_page_events(
                    service, start_date, end_date, page_token=next_page_token
                )
        except Exception as e:
            logger.error(f"Error fetching events from the primary google calendar of {self.user_email}: {e}")

        if len(keywords) == 0:
            return all_events

        keywords_arr = [x.strip() for x in keywords.split(",")]

        return list(filter(partial(self.filter_events, keywords=keywords_arr), all_events))

    def get_newest_events(
        self,
        time_min,
        max_results=10,
        keywords: str = settings.GOOGLE_CALENDAR_EVENTS_KEYWORDS,
    ):
        self.authorize()
        service = build("calendar", "v3", credentials=self.creds)

        events_result = (
            service.events()
            .list(
                calendarId="primary",
                timeMin=time_min,
                maxResults=max_results,
                singleEvents=True,
                orderBy="startTime",
            )
            .execute()
        )

        events = events_result.get("items", [])
        keywords_arr = [x.strip() for x in keywords.split(",")]

        return list(filter(partial(self.filter_events, keywords=keywords_arr), events))

    def get_events_by_sync_token(
        self,
        sync_token: str | None = None,
        keywords: str = settings.GOOGLE_CALENDAR_EVENTS_KEYWORDS,
    ):
        """
        Fetch calendar events using a sync token or time-based query.

        Args:
            sync_token: Optional token for incremental sync
            keywords: Comma-separated list of keywords to filter events

        Returns:
            tuple: (filtered_events, next_sync_token)
        """
        self.authorize()
        service = build("calendar", "v3", credentials=self.creds)

        events = []
        next_sync_token = None

        if sync_token:
            try:
                events_result = (
                    service.events()
                    .list(
                        calendarId="primary",
                        syncToken=sync_token,
                        singleEvents=True,
                    )
                    .execute()
                )

                next_sync_token = events_result.get("nextSyncToken")
                events = events_result.get("items", [])

            except HttpError as error:
                errors = self.parse_calendar_api_errors(error)
                if "fullSyncRequired" in errors:
                    # Fall back to time-based fetch without recursion
                    sync_token = None
                else:
                    if hasattr(error, "resp") and error.resp.status in (401, 403):
                        return [], None
                    raise error

        # If no sync token or fullSyncRequired error occurred
        if not sync_token:
            # Get recent events (last 10 minutes)
            time_min = (datetime.now() - timedelta(minutes=10)).isoformat() + "Z"
            try:
                events_result = (
                    service.events()
                    .list(
                        calendarId="primary",
                        timeMin=time_min,
                        maxResults=10,
                        singleEvents=True,
                        orderBy="startTime",
                    )
                    .execute()
                )

                # Get sync token from a separate request
                token_result = (
                    service.events()
                    .list(
                        calendarId="primary",
                        singleEvents=True,
                    )
                    .execute()
                )

                next_sync_token = token_result.get("nextSyncToken")
                events = events_result.get("items", [])

            except HttpError as error:
                # Handle authentication errors
                if hasattr(error, "resp") and error.resp.status in (401, 403):
                    return [], None
                raise error

        # Filter events by keywords
        keywords_list = [keyword.strip() for keyword in keywords.split(",")]
        filtered_events = list(filter(partial(self.filter_events, keywords=keywords_list), events))

        return filtered_events, next_sync_token

    def get_page_events(
        self,
        service,
        start_date: datetime,
        end_date: datetime,
        calendar_id: str | None = None,
        page_token: str | None = None,
    ):
        events_result = (
            service.events()
            .list(
                calendarId=calendar_id or "primary",
                maxResults=1000,
                singleEvents=True,
                orderBy="startTime",
                timeMin=start_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                timeMax=end_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                pageToken=page_token,
            )
            .execute()
        )
        events = events_result.get("items", [])
        next_page_token = events_result.get("nextPageToken")

        return events, next_page_token

    def create_event(
        self,
        title: str,
        date_start: datetime,
        date_end: datetime,
        description: str = "",
        timezone_start: str | None = None,
        timezone_end: str | None = None,
        location: str = "",
    ):
        event = {
            "summary": title,
            "start": {
                "dateTime": (date_start.strftime("%Y-%m-%dT%H:%M:%S.%f") if date_start else None),
                "timeZone": timezone_start,
            },
            "end": {
                "dateTime": (date_end.strftime("%Y-%m-%dT%H:%M:%S.%f") if date_end else None),
                "timeZone": timezone_end,
            },
            "reminders": {
                "useDefault": True,
            },
        }

        if len(description) > 0:
            event["description"] = description

        if len(location) > 0:
            event["location"] = location

        self.authorize()

        service = build("calendar", "v3", credentials=self.creds)

        try:
            event = service.events().insert(calendarId=self.calendar_id, body=event).execute()
            return event.get("id", None)
        except Exception as e:
            logger.error(f"Error creating calendar event [title: {title}] for user {self.user_email}. Error: {e}")
            return None

    def search_event(self, query: str):
        self.authorize()
        service = build("calendar", "v3", credentials=self.creds, cache_discovery=False)

        events_result = (
            service.events().list(calendarId=self.calendar_id, maxResults=10, singleEvents=True, q=query).execute()
        )
        events = events_result.get("items", [])

        return events

    def delete_event(self, event_id: str):
        self.authorize()
        service = build("calendar", "v3", credentials=self.creds, cache_discovery=False)

        service.events().delete(calendarId=self.calendar_id, eventId=event_id).execute()

    def update_event_overwrite(
        self,
        event_id: str,
        title: str,
        date_start: datetime,
        date_end: datetime,
        description: str = "",
        timezone_start: str | None = None,
        timezone_end: str | None = None,
    ):
        event = {
            "summary": title,
            "start": {
                "dateTime": (date_start.strftime("%Y-%m-%dT%H:%M:%S.%f") if date_start else None),
                "timeZone": timezone_start,
            },
            "end": {
                "dateTime": (date_end.strftime("%Y-%m-%dT%H:%M:%S.%f") if date_end else None),
                "timeZone": timezone_end,
            },
            "reminders": {
                "useDefault": True,
            },
        }

        if len(description) > 0:
            event["description"] = description

        self.authorize()

        service = build("calendar", "v3", credentials=self.creds)

        try:
            event = service.events().update(calendarId=self.calendar_id, eventId=event_id, body=event).execute()
            return event.get("id", None)
        except Exception as e:
            logger.error(f"Error updating calendar event [new title: {title}] for user {self.user_email}. Error: {e}")
            return None

    @staticmethod
    def filter_events(event, keywords):
        text_fields = ["summary", "description", "location"]
        for k in keywords:
            for field in text_fields:
                if k.upper() in event.get(field, "").upper():
                    return True

        return False

    @staticmethod
    def extract_relevant_fields(events: List[Dict]) -> List[Dict]:
        return list(
            map(
                lambda x: {
                    "id": x.get("id", ""),
                    "summary": x.get("summary", ""),
                    "description": x.get("description", ""),
                    "location": x.get("location", ""),
                    "start": x.get("start", None),
                    "end": x.get("end", None),
                    "is_new": False,
                },
                events,
            )
        )

    def update_event(self, event_id: str, updates: Dict[str, Any]):
        # The updates is a dict, the key(s) must be valid event keys
        # For example: updates = {"description": "This will update the event
        # description."}

        self.authorize()
        service = build("calendar", "v3", credentials=self.creds)

        try:
            event = service.events().get(calendarId=self.calendar_id, eventId=event_id).execute()

            for k in updates.keys():
                if k in event.keys():
                    event[k] += f"\n{updates[k]}"

            service.events().update(calendarId=self.calendar_id, eventId=event["id"], body=event).execute()
        except Exception as e:
            logger.error(f"Error updating calendar event [id: {event_id}] for user {self.user_email}. Error: {e}")
            if isinstance(e, HTTPException):
                raise e
            elif isinstance(e, HTTPError):
                raise HTTPException(status_code=e.response.status_code, detail=e.response.text)
            else:
                raise HTTPException(status_code=500, detail=e.args[0])

    def watch_calendar(self):
        self.authorize()

        try:
            service = build("calendar", "v3", credentials=self.creds)

            # Create a hash of the email
            hash_object = hashlib.md5(f"{self.refresh_token}".encode())

            # Generate a UUID using the hash
            generated_uuid = uuid.UUID(hash_object.hexdigest())

            request_body = {
                "id": str(generated_uuid),
                "type": "web_hook",
                "address": f"{settings.SERVER_DNS}/api/google/calendar-webhook?shared_secret={self.refresh_token}&user_email={self.user_email}",
            }

            response = service.events().watch(calendarId=self.calendar_id, body=request_body).execute()
            return response.get("resourceId")
        except Exception as e:
            logger.error(f"Error watching calendar for user {self.user_email}. Error: {e}")

    def stop_watch(self, channel_id, resource_id):
        self.authorize()

        try:
            service = build("calendar", "v3", credentials=self.creds)

            body = {"id": channel_id, "resourceId": resource_id}

            request = service.channels().stop(body=body)
            response = request.execute()
            return response

        except Exception as e:
            logger.warning(f"Error stopping watch for user {self.user_email}. Error: {e}")

    def list_calendar_ids(self):
        """
        Lists all available calendars for the authenticated user.
        Returns a list of dictionaries containing calendar id and summary.
        """
        self.authorize()
        service = build("calendar", "v3", credentials=self.creds)

        try:
            calendar_list = service.calendarList().list().execute()
            calendars = calendar_list.get("items", [])
            return [{"id": calendar["id"], "summary": calendar["summary"]} for calendar in calendars]
        except Exception as e:
            logger.warning(
                f"Failed to list calendars for user {self.user_email}. Error: {e}, use the primary calendar only"
            )
            return [{"id": "primary", "summary": "Primary"}]

    @staticmethod
    def parse_calendar_api_errors(error: HttpError) -> list[str]:
        try:
            reasons = []
            error_details = json.loads(error.content.decode())

            if "error" in error_details:
                error_info = error_details["error"]

                if "errors" in error_info:
                    for specific_error in error_info["errors"]:
                        reasons.append(specific_error.get("reason"))

            return reasons
        except json.JSONDecodeError:
            logger.warning(f"Failed to decode error response as JSON. Raw error content: {error.content.decode()}")
            return []
