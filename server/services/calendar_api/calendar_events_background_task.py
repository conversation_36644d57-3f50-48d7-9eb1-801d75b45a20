import traceback
from datetime import datetime, timedelta, timezone

from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.utils.logger import logger
from server.utils.mongo_connector import google_calendar_events_collection


async def background_get_google_calendar_events(user: User):
    try:
        start_date = datetime.now(tz=timezone.utc)
        end_date = datetime.now(tz=timezone.utc) + timedelta(days=60)

        user_profile = await UserProfile.from_user_id(user.id)
        if user_profile is None:
            logger.error(f"User profile not found for user_id: {user.id} {user.email}")
            return

        events_api = CalendarProviderManager(user_profile=user_profile)
        events = events_api.get_events(start_date, end_date)

        events_processed = list(
            map(
                lambda x: {
                    "summary": x.get("summary", ""),
                    "description": x.get("description", ""),
                    "location": x.get("location", ""),
                    "start": x.get("start", None),
                    "end": x.get("end", None),
                },
                events,
            )
        )
        await google_calendar_events_collection.update_one(
            {"user_id": user.id},
            {"$set": {"user_id": user.id, "events": events_processed}},
            upsert=True,
        )
    except BaseException:
        logger.error(traceback.format_exc())
