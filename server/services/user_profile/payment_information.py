import asyncio
from typing import Any

from server.api.v1.endpoints.user_profile.personal_information import save_user_personal_information
from server.schemas.authenticate.user import User
from server.schemas.user_profile.payment_information import (
    ManagedTravelerAccess,
    PaymentInformationRequest,
    PaymentSource,
)
from server.schemas.user_profile.personal_information import PersonalInformationExtendedRequest
from server.utils.mongo_connector import user_profile_payment_information_collection
from server.utils.spotnana_api import spotnana_api


async def get_user_profile_payment_information(user_id: int, is_company_card: bool = False):
    query: dict[str, Any] = {"users_id": user_id}
    if is_company_card:
        query["isCompanyCard"] = True

    user_payment_information = await user_profile_payment_information_collection.find_one(query, {"_id": 0})

    return user_payment_information


async def save_payment_information_spotnana(payment_profile: PaymentInformationRequest, user: User):
    traveler_info: dict[str, Any] | None = await spotnana_api.get_traveler_by_email(user.email)
    if traveler_info is None or len(traveler_info["results"]) == 0:
        personal_information: PersonalInformationExtendedRequest = PersonalInformationExtendedRequest(
            title="TITLE_UNKNOWN",
            first_name=user.first_name,
            last_name=user.last_name,
            dob="",
            gender="UNSPECIFIED",
            phone="",
        )
        await save_user_personal_information(personal_information, user)

        traveler_info: dict[str, Any] | None = await spotnana_api.get_traveler_by_email(user.email)

    assert traveler_info is not None and len(traveler_info["results"]) > 0, "Missing traveler profile"

    user_org_id = traveler_info["results"][0]["userOrgId"]

    traveler_cards = (await spotnana_api.traveler_read(user_org_id)).get("traveler").get("user").get("paymentInfos")  # type: ignore
    if len(traveler_cards) > 0:
        await asyncio.gather(
            *[spotnana_api.delete_credit_card(card.get("card").get("id"), user_org_id) for card in traveler_cards]
        )

    expiry: list[str] = (payment_profile.exp_date or "").split(" / ")
    expiry_month: str = expiry[0]
    expiry_year: str = f"20{expiry[1]}" if len(expiry[1]) == 2 else expiry[1]

    spotnana_payment_source_dict: dict[str, Any] = {
        "paymentSourceInfo": {
            "paymentSource": {
                "type": "CARD",
                "card": {
                    "company": map_card_company_to_spotnana_types((payment_profile.card_type or "none").upper()),
                    "type": "CREDIT",  # [ UNKNOWN, CREDIT, DEBIT, UNRECOGNIZED ]
                    "address": {
                        "regionCode": "US",
                        "postalCode": payment_profile.zip_code,
                        "administrativeArea": payment_profile.state,
                        "locality": payment_profile.city,
                        "addressLines": [payment_profile.address],
                        "description": "",
                    },
                    "label": "OTTO credit card",
                    "name": payment_profile.cardholder_name,
                    "number": payment_profile.card_number,
                    "expiry": {
                        "expiryMonth": expiry_month,
                        "expiryYear": expiry_year,
                    },
                    "cvv": payment_profile.card_cvc,
                },
            },
            "filter": {"type": "PERSONAL", "travelTypes": ["AIR", "HOTEL"]},
        },
    }
    spotnana_payment_source: PaymentSource = PaymentSource(**spotnana_payment_source_dict)

    # Make it available to all travelers associated with this traveler
    if payment_profile.isCompanyCard:
        spotnana_payment_source.paymentSourceInfo.filter.managedTravelerAccess = ManagedTravelerAccess(
            **{"enabled": True, "visibility": {"showToTravelers": True}}
        )

    await spotnana_api.create_payment_source(user_org_id.get("userId", {}).get("id"), spotnana_payment_source)


def is_user_profile_payment_information_complete(payment_information: dict[str, Any] | None):
    if payment_information is None:
        return False

    required_fields = [
        "address",
        "city",
        "state",
        "zip_code",
        "card_number",
        "card_cvc",
        "cardholder_name",
        "exp_date",
    ]
    empty_fields = [field for field in required_fields if payment_information.get(field, "") == ""]

    return len(empty_fields) == 0


def get_missing_user_profile_payment_information_fields(
    payment_information: dict[str, Any] | None,
) -> tuple[bool, list[str] | None]:
    if payment_information is None or len(payment_information) == 0:
        return (False, None)

    required_fields = [
        "address",
        "city",
        "state",
        "zip_code",
        "card_number",
        "card_cvc",
        "cardholder_name",
        "exp_date",
    ]
    empty_fields = [field for field in required_fields if payment_information.get(field, "") == ""]

    return (True, empty_fields)


def map_card_company_to_spotnana_types(card_company: str):
    # [ NONE, VISA, MASTERCARD, AMEX, DISCOVER, AIR_TRAVEL_UATP, CARTE_BLANCHE, DINERS_CLUB, JCB, EURO_CARD, ACCESS_CARD, BREX, UNION_PAY, UNRECOGNIZED ]
    if card_company == "DINERSCLUB":
        card_company = "DINERS_CLUB"

    if card_company == "UNIONPAY":
        card_company = "UNION_PAY"

    if card_company == "ELO":
        card_company = "ELO_CARD"

    return card_company
