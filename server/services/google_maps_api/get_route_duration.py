from fastapi.exceptions import HTTPException

from server.utils.async_requests import make_post_request
from server.utils.logger import logger
from server.utils.settings import settings


async def get_route_duration(
    origin_lat: float, origin_lng: float, destination_lat: float, destination_lng: float, travel_mode: str = "DRIVE"
) -> float | None:
    """
    Asynchronously call Google Routes API to get travel duration between two locations.

    Args:
        origin_lat: Latitude of the origin location
        origin_lng: Longitude of the origin location
        destination_lat: Latitude of the destination location
        destination_lng: Longitude of the destination location
        travel_mode: Travel mode (DRIVE, WALK, BICYCLE, TRANSIT). Defaults to DRIVE.

    Returns:
        Duration in seconds as a float, or None if the request fails
    """

    url = "https://routes.googleapis.com/directions/v2:computeRoutes"

    data = {
        "origin": {"location": {"latLng": {"latitude": origin_lat, "longitude": origin_lng}}},
        "destination": {"location": {"latLng": {"latitude": destination_lat, "longitude": destination_lng}}},
        "travelMode": travel_mode,
        "routingPreference": "TRAFFIC_AWARE",
        "computeAlternativeRoutes": False,
        "routeModifiers": {"avoidTolls": False, "avoidHighways": False, "avoidFerries": False},
        "languageCode": "en-US",
        "units": "METRIC",
    }

    headers = {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": settings.GOOGLE_MAPS_API_KEY,
        "X-Goog-FieldMask": "routes.duration",
    }

    try:
        response_data = await make_post_request(url=url, headers=headers, data=data)
        logger.info(f"Google Routes API response: {response_data}")

        if (
            response_data.get("routes")
            and len(response_data["routes"]) > 0
            and response_data["routes"][0].get("duration")
        ):
            duration_str = response_data["routes"][0]["duration"]
            if duration_str.endswith("s"):
                return float(duration_str[:-1])

        logger.warning("No valid duration found in Google Routes API response")
        return None

    except HTTPException as e:
        logger.error(f"Error calling Google Routes API: {e}")
        return None
