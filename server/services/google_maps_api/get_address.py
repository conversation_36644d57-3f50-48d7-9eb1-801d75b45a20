import aiohttp

from server.utils.logger import logger
from server.utils.mongo_connector import google_maps_cache_collection
from server.utils.settings import settings


async def get_address_from_coordinates(lat: float, lng: float) -> str:
    try:
        mongo_doc = await google_maps_cache_collection.find_one({"lat": lat, "lng": lng})
        if mongo_doc:
            return mongo_doc["address"]

        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"https://maps.googleapis.com/maps/api/geocode/json?latlng={lat},{lng}&key={settings.GOOGLE_MAPS_API_KEY}"
            ) as res:
                data = await res.json()
                address = data["results"][0]["formatted_address"]

                await google_maps_cache_collection.insert_one({"lat": lat, "lng": lng, "address": address})
                return address
    except Exception as e:
        logger.error(f"Error getting address from coordinates: {e}")
        return ""
