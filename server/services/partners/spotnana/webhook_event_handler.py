import asyncio
from typing import Any, Callable, Coroutine

import aiohttp
import boto3
from botocore.config import Config

from in_trip.in_trip import InTripAgent
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.schemas.authenticate.user import User as UserSchema
from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.schemas.spotnana.flight_statuses import FlightStatuses
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.partners.spotnana.webhook_event_handler_cancel_flight import handle_webhook_ticket_refunded
from server.services.partners.spotnana.webhook_event_handler_exchange_flight import (
    handle_webhook_booking_other_update,
)
from server.services.trips.flight_card import construct_flight_itinerary_dict
from server.services.trips.spotnana_itinerary import get_spotnana_flight_itinerary
from server.utils.booking_utils import extract_booking_dates_and_status
from server.utils.logger import logger
from server.utils.settings import settings


async def handle_spotnana_webhook_event(
    data: SpotnanaTravelDelivery, route_to_staging: Callable[[SpotnanaTravelDelivery], Coroutine[Any, Any, None]]
):
    payload = data.payload
    trip_id: str | None = payload.get("tripId", None)

    if trip_id is None:
        logger.error(f"[SPOTNANA WEBHOOK] Trip id not found in payload {data.model_dump()}")

    existing_trip: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if existing_trip is None:
        if settings.OTTO_ENV.lower() != "live":
            logger.error(f"[SPOTNANA WEBHOOK] Trip id {trip_id} not found in database, might be an issue.")
        else:
            logger.warning(
                f"[SPOTNANA WEBHOOK] Trip id {trip_id} not found in database, might be in staging, redicting to staging"
            )
            asyncio.create_task(route_to_staging(data))
    else:
        match data.event_type:
            case "PNR_V3":
                match data.operation:
                    case "TICKET_REFUNDED" | "FLIGHT_CANCELLED" | "TICKET_VOIDED":
                        await handle_webhook_ticket_refunded(data.payload)

                    case "BOOKING_OTHER_UPDATE" | "BOOKING_TICKETED" | "SERVICE_FEE":
                        if data.payload.get("bookingHistory", [{}])[0].get("bookingInfo", {}).get("status") == "BOOKED":
                            # Booking confirmed
                            await handle_webhook_booking_confirmed(data.payload)
                            logger.info(
                                f"[SPOTNANA WEBHOOK] Booking confirmed, trip id {data.payload.get('tripId', None)}"
                            )

                        elif (
                            data.payload.get("bookingHistory", [{}])[0].get("bookingInfo", {}).get("status")
                            == "EXCHANGED"
                        ):
                            # Flight exchanged, update the data in mongo and google calendar
                            await handle_webhook_booking_other_update(data.payload)

                    case "INVOICE_GENERATED":
                        await handle_webhook_invoice_generated(payload)

        if data.event_type == "PNR_V3" and data.operation in [
            "BOOKING_CANCELED_BY_VENDOR",
            "FLIGHT_SCHEDULE_CHANGE_PENDING",
            "FLIGHT_SCHEDULE_CHANGE_CLOSED",
            "TICKET_VOIDED",
        ]:
            await handle_webhook_flight_status_change(data)


async def handle_webhook_flight_status_change(data: SpotnanaTravelDelivery):
    payload = data.payload
    trip_id: str = payload.get("tripId", None)

    booking: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if booking is None:
        logger.error(f"[SPOTNANA WEBHOOK] Booking not found for trip id {trip_id}")
        return

    thread_info: ChatThread | None = await ChatThread.from_id(booking.thread_id)
    if thread_info is None:
        logger.error(f"[SPOTNANA WEBHOOK] Booking not found for trip id {trip_id}")
        return

    user = await User.from_id(thread_info.users_id)
    if user is None:
        logger.error(f"[SPOTNANA WEBHOOK] Booking not found for trip id {trip_id}")
        return

    in_trip_enabled = await is_feature_flag_enabled(user.id, FeatureFlags.ENABLE_IN_TRIP)
    is_in_trip = await InTripAgent.is_in_trip_booking(booking)

    if not in_trip_enabled or not is_in_trip:
        logger.info(
            f"[SPOTNANA WEBHOOK] In trip not enabled for user {user.id} or trip {booking.thread_id} is not in trip, skipping."
        )
        return

    user = UserSchema.from_orm_user(user)
    in_trip_agent = InTripAgent(user, thread_info)
    await in_trip_agent.handle_flight_changes_for_in_trip(data, booking, thread_info, user)


async def handle_webhook_booking_confirmed(payload):
    trip_id: str = payload.get("tripId", None)

    booking: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if booking is None:
        return

    # Check if the booking is already confirmed
    if booking.content.get("status") != FlightStatuses.BOOKED.value:
        flat_trip_details = get_spotnana_flight_itinerary(
            {"pnrs": [{"data": payload}], "tripPaymentInfo": {"totalFareAmount": payload.get("totalFareAmount", {})}}
        )
        updated_content = {
            **booking.content,
            **construct_flight_itinerary_dict(
                flat_trip_details.to_flight_option_dict_list(), booking.content.get("confirmation_id")
            ),
            "status": FlightStatuses.BOOKED.value,
        }
        start_date, end_date, status = extract_booking_dates_and_status("flight", updated_content)
        await Booking.update_fields(
            {"id": booking.id},
            {
                "content": updated_content,
                "start_date": start_date,
                "end_date": end_date,
                "status": status.name if status else None,
            },
        )
        logger.info(f"Trip {trip_id} flight confirmed!")


async def handle_webhook_invoice_generated(payload):
    trip_id: str = payload.get("tripId", None)

    booking: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
    if booking is None:
        return

    invoices: list[str] = booking.content.get("invoices", [])

    for document in payload.get("documents", [{}]):
        document_metadata = document.get("documentMetadata", {})
        filename_no_ext = document_metadata.get("name", "invoice").removesuffix(".pdf")
        s3_key = f"{trip_id}/{filename_no_ext}.pdf"
        aws_url: str = construct_aws_url(settings.INVOICE_S3_BUCKET, s3_key)

        # Do not re-upload
        if aws_url in invoices:
            continue

        if document.get("url", None) and document_metadata.get("documentType", None) == "INVOICE":
            document_url = document.get("url", "")
            logger.info(f"Receive invoice file for trip {trip_id} at {document_url}")

            await upload_invoice_to_s3(document_url=document_url, trip_id=trip_id, s3_key=s3_key)
            invoices.append(aws_url)

    # Update if we have new invoices
    if len(invoices) > len(booking.content.get("invoices", [])):
        await Booking.update_fields({"id": booking.id}, {"content.invoices": invoices})


async def upload_invoice_to_s3(document_url: str, trip_id: str, s3_key: str):
    if not document_url:
        logger.error(f"Empty document_url {document_url} for the invoice of trip {trip_id}")
        return

    try:
        s3_client = boto3.client(
            "s3",
            config=Config(
                retries={
                    "max_attempts": 3,
                    "mode": "standard",  # static backoff retry
                }
            ),
        )

        async with aiohttp.ClientSession() as session:
            async with session.get(document_url) as response:
                if response.status == 200:
                    pdf_bytes = await response.read()

                    bucket = settings.INVOICE_S3_BUCKET
                    s3_client.put_object(
                        Bucket=bucket,
                        Key=s3_key,
                        Body=pdf_bytes,
                        ContentType="application/pdf",
                    )
                    logger.info(f"Uploaded invoice PDF to s3://{bucket}/{s3_key}")
                else:
                    logger.error(
                        f"Failed to download invoice PDF from {document_url}: {response.status} {response.reason}"
                    )
    except Exception as e:
        logger.error(f"Error transferring invoice PDF to S3: {e}")


def construct_aws_url(bucket: str, key: str):
    return f"https://{bucket}.s3.amazonaws.com/{key}"
