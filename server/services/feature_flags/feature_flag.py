from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, EmailStr

from server.utils.mongo_connector import user_feature_flags_collection


class FeatureFlags(str, Enum):
    """Enum defining available feature flags"""

    ENABLE_MEMORY_RETRIEVAL = "enable_memory_retrieval"
    ENABLE_FUTURE_TRIP_NOTIFICATIONS = "enable_future_trip_notifications"
    ENABLE_SPOTNANA_HOTELS_API = "enable_spotnana_hotels_api"
    ENABLE_ASYNC_PLAN = "enable_async_plan"
    ENABLE_CLAUDE_SUPERVISOR = "enable_claude_supervisor"
    ENABLE_BAGGAGE_CHECK = "enable_baggage_check"
    ENABLE_IN_TRIP = "enable_in_trip"
    ENABLE_SAVED_TRIP = "enable_saved_trip"
    CAPABILITY_PROMPTING_ENABLED = "capability_prompting_enabled"
    ENABLE_FORK_NEW_TRIP = "enable_fork_new_trip"
    # Add more feature flags as needed


class UserFeatureFlags(BaseModel):
    email: EmailStr
    user_id: int
    feature_flags: List[FeatureFlags | str]

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "user_id": 12345,
                "feature_flags": [FeatureFlags.ENABLE_MEMORY_RETRIEVAL],
            }
        }


async def create_feature_flag(user_id: int, email: str, feature_flag: FeatureFlags) -> dict:
    """Create a new feature flag entry in the database."""
    # First check if the feature flag already exists
    existing_flags = await user_feature_flags_collection.find_one({"user_id": user_id, "email": email})

    if existing_flags and feature_flag.value in existing_flags.get("feature_flags", []):
        return {"message": "Feature flag already exists"}

    await user_feature_flags_collection.update_one(
        {"user_id": user_id, "email": email},
        {"$addToSet": {"feature_flags": feature_flag}},
        upsert=True,
    )

    return {"message": "Feature flag created"}


async def remove_feature_flag(user_id: int, email: str, feature_flag: FeatureFlags) -> dict[str, Any] | None:
    """Remove feature flag from database for a specific user."""
    await user_feature_flags_collection.update_one(
        {"user_id": user_id, "email": email},
        {"$pull": {"feature_flags": feature_flag}},
    )

    return {"message": "Feature flag removed"}


async def get_feature_flags_by_user_id(user_id: int) -> Optional[UserFeatureFlags]:
    """Retrieve feature flags for a specific user by user_id."""
    if feature_flag := await user_feature_flags_collection.find_one({"user_id": user_id}):
        return UserFeatureFlags(**feature_flag)
    return None


async def is_feature_flag_enabled(user_id: int, flag: FeatureFlags) -> bool:
    """Check if a feature flag is enabled for a specific user."""
    feature_flag = await get_feature_flags_by_user_id(user_id)
    return flag in feature_flag.feature_flags if feature_flag else False
