import asyncio
import itertools
import json
from datetime import datetime, timezone
from functools import partial
from typing import Any, Coroutine, List

from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage, HumanMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.graph import CompiledGraph

from baml_client import b
from baml_client.types import (
    CalendarType,
    CompanyPolicy,
    FormattedText,
    OnboardingConversation,
    OnboardingStep,
    PreferencesConversation,
    ResponseAllPreferences,
)
from llm_utils.llm_utils import get_message_buffer_as_strings, load_past_calendar_events
from onboarding_agent.models import AgentState
from onboarding_agent.prompts import (
    ASK_CALENDAR_ACCESS_PROMPT_FOR_SKIP_INTENTION,
    ASK_FOR_CALENDAR_ACCESS,
    ASK_MANUAL_SET_PROMPT_FOR_SKIP_INTENTION,
    FORCE_SKIP_PROMPT,
    OPENING_STRING,
    OPENING_WITHOUT_NAME,
    OTP_USER_CHOOSE_CALENDAR_ACCESS,
    OTTO_KNOW_MORE_EXPLANATION,
    SKIP_PROMPT_COMBINE_INSTRUCTION,
    THIRD_PARTY_CALENDAR_ACCESS,
)
from server.database.models.user import User as UserDB
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.authenticate.microsoft import get_microsoft_auth_url
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.user.user_preferences import save_user_preferences
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.analytics.zapier import ZapierClient
from server.utils.google_login import get_google_auth_url
from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.helpers import console_masks
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import LatencyTimer, Timings

config: RunnableConfig = {"configurable": {"recursion_limit": 10}}
other_log_mask = console_masks["other"]


class OnboardingAgent:
    def __init__(
        self,
        user: User,
        thread_id: int,
        websocket_send_message: partial[Coroutine[Any, Any, None]] | None,
        exsiting_user_preferences: ResponseAllPreferences | None = None,
    ):
        self.websocket_send_message = websocket_send_message if websocket_send_message is not None else partial_no_op

        self.user: User = user
        self.thread_id = thread_id

        # Add memory
        self.history = PostgresChatMessageHistory(thread_id=thread_id)

        self.messages: list[BaseMessage] = []

        # Define a new graph
        self.workflow: StateGraph = StateGraph(AgentState)

        self.workflow.add_node("onboarding_convo_agent", self.onboarding_convo_agent_model_function)
        self.workflow.add_node("get_calendar_access", self.get_calendar_access_function)
        self.workflow.add_node("get_calendar_events_and_analysis", self.get_calendar_events_and_analysis_function)

        self.workflow.add_conditional_edges(
            "onboarding_convo_agent",
            self.handle_onboarding_convo_next_step,
            {
                "get_calendar_access": "get_calendar_access",
                "get_calendar_events_and_analysis": "get_calendar_events_and_analysis",
                "end": END,
            },
        )

        # This means that this node is the first one called
        self.workflow.set_entry_point("onboarding_convo_agent")

        # This compiles it into a LangChain Runnable,
        # meaning you can use it as you would any other runnable
        self.graph: CompiledGraph = self.workflow.compile()

        self.previous_preference = exsiting_user_preferences
        self.onboarding_skip_attempted = False

    def has_preference_set(self):
        if self.previous_preference is None:
            return False

        return any(value for value in self.previous_preference.model_dump().values())

    async def handle_skip_onboarding_function(self, response: OnboardingConversation):
        response_dict = response.model_dump()
        new_message = AIMessage(content="")

        user_profile: UserProfile | None = await UserProfile.from_user_id(self.user.id)

        has_calendar_access = False
        if user_profile is not None:
            calendar_api = CalendarProviderManager(user_profile=user_profile)
            has_calendar_access = calendar_api.has_calendar_access()

        is_conversation_finished = response_dict.get("is_conversation_finished", False)

        # If user has already attempted skipping once or has preference set, then we should skip the onboarding for the second request for skipping onboarding
        if self.onboarding_skip_attempted:
            is_conversation_finished = True
            response_dict["agent_response"] = FORCE_SKIP_PROMPT
            logger.info(
                f"Skipping onboarding: onboarding_skip_attempted = {self.onboarding_skip_attempted}, has_preference_set = {self.has_preference_set()}, has_calendar_access = {has_calendar_access}"
            )
            await ZapierClient.log_event_in_background(
                event_type=TrackingEvent.ONBOARDING_SKIPPED,
                user_id=str(self.user.id),
                user_email=self.user.email,
            )
        else:
            if response_dict.get("declined_calendar_access", False):
                textToAppend = ASK_MANUAL_SET_PROMPT_FOR_SKIP_INTENTION
            else:
                textToAppend = ASK_CALENDAR_ACCESS_PROMPT_FOR_SKIP_INTENTION

            combined_result = await self.streaming_combine_text(
                response.agent_response, textToAppend, SKIP_PROMPT_COMBINE_INSTRUCTION
            )
            response_dict["agent_response"] = combined_result.combinedText

        response_dict["is_conversation_finished"] = is_conversation_finished
        logger.info(f"Skipping onboarding attempt: {response_dict}")
        arguments_dict = json.dumps(response_dict)
        new_message.additional_kwargs = {"function_call": {"arguments": arguments_dict}}
        return new_message

    async def get_calendar_events_and_analysis_function(self, state):
        events = await load_past_calendar_events(self.user.id)
        messages = state["messages"]
        message_buffer_strs = get_message_buffer_as_strings(messages)
        response = await self.streaming_preference(message_buffer_strs, events)

        new_message = FunctionMessage(
            content="",
            name="analyze_calendar",
        )
        json_representation = response.model_dump_json()
        new_message.additional_kwargs = {"function_call": {"name": new_message.name, "arguments": json_representation}}

        return {"messages": [new_message]}

    async def get_calendar_access_function(self, state: AgentState):
        user_profile = await UserProfile.from_user_id(self.user.id)

        messages = state["messages"]
        last_message = messages[-1]
        arguments_string = last_message.additional_kwargs["function_call"]["arguments"]
        arguments_obj = json.loads(arguments_string)
        last_message.content = arguments_obj["agent_response"]
        calendar_to_connect = arguments_obj["calendar_to_connect"]

        # if user didn't select a calendar, use the last login method or last calendar they connected
        if not calendar_to_connect and user_profile is not None:
            calendar_to_connect = user_profile.last_login_method

        # Define supported calendars
        supported_calendars = [
            CalendarType.Google.name,
            CalendarType.Microsoft.name,
        ]

        # Create response message
        new_message = FunctionMessage(content="", name="grant_calendar_access")
        new_message.additional_kwargs = {
            "function_call": {
                "name": new_message.name,
                "arguments": "",
            }
        }

        # Check if calendar selection is valid
        if not calendar_to_connect or calendar_to_connect not in supported_calendars:
            new_message.additional_kwargs["function_call"]["arguments"] = json.dumps(
                {
                    "agent_response": "Please select a calendar to connect: Google or Microsoft.",
                }
            )
            return {"messages": [new_message]}

        # Handle case for user without profile
        if user_profile is None:
            agent_response = "I'm starting the process of granting me access to your calendar..."
            log_in_url = None
            login_type = None

            if calendar_to_connect == CalendarType.Google.name:
                login_type = "google_login"
                log_in_url = get_google_auth_url(login_hint=self.user.email)
            elif calendar_to_connect == CalendarType.Microsoft.name:
                login_type = "microsoft_login"
                log_in_url = get_microsoft_auth_url(state="onboarding", scopes=["User.Read", "Calendars.ReadWrite"])

            new_message.additional_kwargs["function_call"]["arguments"] = json.dumps(
                {
                    "agent_response": agent_response,
                    "login_init_url": str(log_in_url),
                    "login_type": login_type,
                    "user_asked_to_login": calendar_to_connect,
                }
            )

            return {"messages": [new_message]}

        # User has a profile - check for existing calendar access
        calendar_api = CalendarProviderManager(user_profile=user_profile, user_email=self.user.email)
        has_calendar_access = calendar_api.has_calendar_access()

        # User already has access to the requested calendar
        if has_calendar_access and calendar_to_connect == user_profile.last_login_method:
            # Inform user that calendar access exists
            already_have_access_message = AIMessage(
                content="I already have your calendar access. analyzing your calendar, please wait for a moment.",
                additional_kwargs={"is_conversation_finished": True},
            )
            self.messages.append(already_have_access_message)
            self.history.add_pending_message(already_have_access_message)

            # Send websocket message
            await self.websocket_send_message(
                message={
                    "type": "prompt",
                    "text": already_have_access_message.content,
                    "expectResponse": False,
                    "isBotMessage": True,
                }
            )

            # Fetch and process calendar events
            events = await load_past_calendar_events(self.user.id)
            message_strings = get_message_buffer_as_strings(self.messages)
            response = await self.streaming_preference(message_strings, events)

            # Format response
            response_dict = response.model_dump()
            response_dict.pop("is_conversation_finished", None)
            response_dict["preference"] = response_dict.pop("response_all_preferences", None)
            response_dict.pop("login_init_url", None)
            response_dict.pop("login_type", None)
            new_message.additional_kwargs["function_call"]["arguments"] = json.dumps(response_dict)

            return {"messages": [new_message]}
        else:
            # User needs to connect to a calendar
            agent_response = "I'm starting the process of granting me access to your calendar..."
            login_type = None
            login_init_url = None

            if calendar_to_connect == CalendarType.Google.name:
                login_type = "google_login"
                login_init_url = get_google_auth_url(login_hint=self.user.email)
            elif calendar_to_connect == CalendarType.Microsoft.name:
                login_type = "microsoft_login"
                login_init_url = get_microsoft_auth_url(state="onboarding", scopes=["User.Read", "Calendars.ReadWrite"])

            new_message.additional_kwargs["function_call"]["arguments"] = json.dumps(
                {
                    "agent_response": agent_response,
                    "login_init_url": str(login_init_url),
                    "login_type": login_type,
                    "expectResponse": False,
                    "isBotMessage": True,
                }
            )

        return {"messages": [new_message]}

    async def onboarding_convo_agent_model_function(self, state):
        messages = state["messages"]
        user_input = state["input"]
        messages.append(user_input)
        message_buffer_strs = get_message_buffer_as_strings(messages)

        user_profile: UserProfile | None = await UserProfile.from_user_id(self.user.id)

        has_calendar_access = False
        if user_profile is not None:
            calendar_api = CalendarProviderManager(user_profile=user_profile, user_email=self.user.email)
            has_calendar_access = calendar_api.has_calendar_access()

        ask_for_calendar_access = ASK_FOR_CALENDAR_ACCESS.format(
            calendar_access_prompt=OTP_USER_CHOOSE_CALENDAR_ACCESS
            if user_profile is None
            else THIRD_PARTY_CALENDAR_ACCESS
        )

        t = LatencyTimer("BamlStream:ConverseOnboarding")
        stream = b.stream.ConverseOnboarding(
            messages=message_buffer_strs,
            has_calendar_access=has_calendar_access,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            more_explain=OTTO_KNOW_MORE_EXPLANATION,
            ask_for_calendar_access=ask_for_calendar_access,
            baml_options={"collector": logger.collector},
        )

        skip_onboarding_detection_result = await b.DetectSkipOnboarding(
            messages=get_message_buffer_as_strings(self.messages),
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()

        skip_intention_detected = skip_onboarding_detection_result.skipping_probability >= 0.6
        if not skip_intention_detected:
            first_token = True  # Initialize flag
            async for partial_response in stream:
                if partial_response.agent_response is not None:
                    # yielding the control to the event loop so the streamed messages could go out.
                    await asyncio.sleep(0)
                    if first_token:
                        t.end(tag="FirstTokenResponsed")
                        first_token = False
                    await self.websocket_send_message(
                        message={
                            "type": "prompt",
                            "partial": True,
                            "isBotMessage": True,
                            "expectResponse": False,
                            "text": partial_response.agent_response.replace("\n\n", "&NewLine; &NewLine;"),
                        }
                    )

        response = await stream.get_final_response()
        t.end(tag="FinalResponse")
        logger.log_baml()
        if skip_intention_detected:
            logger.warning(
                f"Skip onboarding detected: {user_input.content}, with probability: {skip_onboarding_detection_result.skipping_probability}",
                mask=other_log_mask,
            )
            new_message = await self.handle_skip_onboarding_function(response=response)

            self.onboarding_skip_attempted = True
        else:
            # Hack in cleaner line feeds that the webapp will
            response.agent_response = response.agent_response.replace("\n\n", "&NewLine; &NewLine;")
            new_message = AIMessage(content="")
            response_dict = response.model_dump()
            new_message.additional_kwargs = {"function_call": {"arguments": json.dumps(response_dict)}}
        return {"messages": [new_message]}

    async def handle_onboarding_convo_next_step(self, state):
        messages = state["messages"]

        # Last response from the model
        last_message = messages[-1]
        logger.info(last_message, mask=other_log_mask)

        arguments_string = last_message.additional_kwargs["function_call"]["arguments"]
        arguments_obj = json.loads(arguments_string)
        last_message.content = arguments_obj["agent_response"]  # Note: Creating duplicate content
        step = arguments_obj["step"]

        if step == OnboardingStep.GettingCalendarAccess.name:
            # saving this message as get_calendar_access will return another new message
            self.messages.append(last_message)
            self.history.add_pending_message(last_message)
            await self.websocket_send_message(
                message={
                    **self.map_websocket_message(last_message)[0],
                    "expectResponse": False,
                }
            )
            return "get_calendar_access"
        elif step == OnboardingStep.AnalyzeCalendar.name:
            # saving this message as both get_calendar_events_and_analysis and get_calendar_access would return another new message
            self.messages.append(last_message)
            self.history.add_pending_message(last_message)
            await self.websocket_send_message(
                message={
                    **self.map_websocket_message(last_message)[0],
                    "expectResponse": False,
                }
            )
            user_profile: UserProfile | None = await UserProfile.from_user_id(self.user.id)

            has_calendar_access = False
            if user_profile is not None:
                calendar_api = CalendarProviderManager(user_profile=user_profile, user_email=self.user.email)
                has_calendar_access = calendar_api.has_calendar_access()

            return "get_calendar_events_and_analysis" if has_calendar_access else "get_calendar_access"

        return "end"

    async def run(self, message=None, message_type="text", extra_payload=None):
        to_send: list[dict[str, str | bool | None]] = []

        if message is None:
            to_send = await self.get_history_messages()

            if len(self.messages) == 0:
                # The messages list is empty, send the opening message
                if self.user.name:
                    agent_response = AIMessage(content=OPENING_STRING.format(user_name=self.user.name).strip())
                else:
                    agent_response = AIMessage(content=OPENING_WITHOUT_NAME.strip())
                # explicitly saving the opening message
                self.history.add_pending_message(agent_response)
                self.messages.append(agent_response)
                await self.websocket_send_message(
                    message={
                        **self.map_websocket_message(agent_response)[0],
                    }
                )
                return to_send
            elif isinstance(self.messages[-1], (AIMessage, FunctionMessage)):
                # In the previous session we were waiting for user input,
                # return the entire history from previous session
                to_send[-1]["expectResponse"] = True
                return to_send
            else:  # last history message is a HumanMessage
                # In the previous session we were waiting for LLM, wait for LLM
                # new message then return it with the entire history from
                # previous session
                input_message = self.messages.pop()

                history_message = {
                    "type": "history",
                    "messages": [{**message, "is_history": True} for message in to_send],
                }

                await self.websocket_send_message(message=history_message)
        else:  # got a new message from the client
            if message_type == "update":
                input_message = HumanMessage(
                    content=", ".join(map(str, message)),
                    additional_kwargs={"is_card_update": True},
                )
            elif message_type == "silent_prompt":
                input_message = HumanMessage(content=message)
                input_message.additional_kwargs["message_type"] = message_type
            else:
                input_message = HumanMessage(content=message)
            # explicitly save the inbound message
            self.history.add_pending_message(input_message)
            self.messages.append(input_message)

        response = await self.graph.ainvoke({"messages": list(self.messages), "input": input_message}, config=config)

        lastest_model_message = response["messages"][-1]
        # Only save the last message as prior messages should have been saved
        self.history.add_pending_message(lastest_model_message)
        self.messages.append(lastest_model_message)

        to_send += self.map_websocket_message(lastest_model_message)

        arguments_obj = json.loads(lastest_model_message.additional_kwargs["function_call"]["arguments"])

        if arguments_obj.get("is_conversation_finished", False):
            to_send.append(
                {
                    "type": "onboarding_chat_complete",
                    "expectResponse": True,
                }
            )

            await ZapierClient.log_event_in_background(
                event_type=TrackingEvent.ONBOARDING_COMPLETED,
                user_id=str(self.user.id),
                user_email=self.user.email,
            )

            if arguments_obj.get("company_policy"):
                await TrackingManager.log_event_in_background(
                    event_type=TrackingEvent.COMPANY_POLICY_COLLECTED,
                    user_id=str(self.user.id),
                    user_email=self.user.email,
                    event_properties={"policy_fields_updated": True},
                )

            else:
                await ZapierClient.log_event_in_background(
                    event_type=TrackingEvent.COMPANY_POLICY_SKIPPED,
                    user_id=str(self.user.id),
                    user_email=self.user.email,
                )

        elif arguments_obj.get("is_preference_gathering_finished"):
            to_send.append(
                {
                    "type": "profile_update",
                    "expectResponse": True,
                }
            )
            preferences_dict = arguments_obj.get("preference") or {}
            asyncio.create_task(self.__collect_preference_event(preferences_dict))

        if arguments_obj.get("preference") is not None:
            user_preferences = ResponseAllPreferences(**arguments_obj["preference"])
            if self.previous_preference != user_preferences:
                if arguments_obj.get("step") == "ManualPreferenceCollection":
                    await TrackingManager.log_event_in_background(
                        event_type=TrackingEvent.MANUAL_PREFERENCE_COLLECTED,
                        user_id=str(self.user.id),
                        user_email=self.user.email,
                        event_properties={"preferences_updated": True},
                    )
                self.previous_preference = user_preferences
            logger.info(f"FINAL PREFERENCES JSON: {user_preferences}")
            await save_user_preferences(self.user.id, user_preferences)
        if arguments_obj.get("company_policy") is not None:
            to_be_update_policy = (
                None
                if arguments_obj.get("company_policy") is None
                else CompanyPolicy(**arguments_obj["company_policy"])
            )
            await UserCompanyTravelPolicy.update_company_travel_policy(self.user.id, to_be_update_policy)

        if preferred_name := arguments_obj.get("preferred_name"):
            self.user.preferred_name = preferred_name
            if userdb := await UserDB.from_id(self.user.id):
                await userdb.refresh_fields({"preferred_name": preferred_name})
                to_send.append({"type": "profile_update", "expectResponse": True})

        return to_send

    def map_websocket_message(self, message: BaseMessage) -> list[dict[str, str | bool | None]]:
        is_bot_message: bool = isinstance(message, AIMessage) or isinstance(message, FunctionMessage)

        messages: list[dict[str, str | bool | None]] = []
        try:
            message_dict: dict[str, str] = json.loads(
                message.additional_kwargs.get("function_call", {}).get("arguments", "{}")
            )

            messages.append(
                {
                    "type": message_dict.get("login_type") or message_dict.get("message_type") or "prompt",
                    "text": message_dict.get("agent_response") or str(message.content),
                    "loginInitUrl": message_dict.get("login_init_url"),
                    "loginInitUrlList": message_dict.get("login_init_url_list"),
                    # login_init_url message shouldn't expect response, as the client will redirect to the login page
                    "expectResponse": True if not message_dict.get("login_init_url") else False,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                }
            )

        except ValueError:
            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                }
            )

        except Exception as e:
            logger.error(f"Error mapping message to websocket message: {e}")

            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""),
                        None,
                    ),
                }
            )

        return messages

    async def add_timestamp_message(self, new_timestamp: str | None = None, send_ws_message: bool = False):
        if new_timestamp is None:
            new_timestamp = datetime.now(timezone.utc).isoformat()

        if datetime.fromisoformat(new_timestamp) - self.history.max_created_date > settings.MIN_MESSAGE_TIMESTAMP_DELTA:
            timestamp_message = AIMessage(content="", additional_kwargs={"timestamp": new_timestamp})
            self.history.add_pending_message(timestamp_message)

            if send_ws_message:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "timestamp": timestamp_message.additional_kwargs.get("timestamp"),
                    }
                )

    async def get_history_messages(self):
        self.messages = await self.history.persisted_messages

        history: list[list[dict[str, str | bool | None]]] = []
        for idx, message in reversed(list(enumerate(self.messages))):
            if message.additional_kwargs.get("is_card_update", False):
                continue
            elif message.additional_kwargs.get("timestamp", False):
                history.append(
                    [
                        {
                            "type": "prompt",
                            "timestamp": message.additional_kwargs.get("timestamp"),
                        }
                    ]
                )
                del self.messages[idx]
            else:
                history.append(self.map_websocket_message(message))
        history.reverse()
        to_send = list(itertools.chain.from_iterable(history))

        # This is history, do not wait for user input
        for msg in to_send:
            msg["expectResponse"] = False

        return to_send

    async def __collect_preference_event(self, preferences_dict: dict[str, Any]):
        user_profile: UserProfile | None = await UserProfile.from_user_id(self.user.id)

        has_calendar_access = False
        if user_profile is not None:
            calendar_api = CalendarProviderManager(user_profile=user_profile)
            has_calendar_access = calendar_api.has_calendar_access()

        if any(value for value in preferences_dict.values()):
            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.PREFERENCE_COLLECTION_COMPLETED,
                user_id=str(self.user.id),
                user_email=self.user.email,
                event_properties={"preferences_updated": True},
            )

            if not has_calendar_access:
                await ZapierClient.log_event_in_background(
                    event_type=TrackingEvent.CALENDAR_ACCESS_REJECTED,
                    user_id=str(self.user.id),
                    user_email=self.user.email,
                )

    async def streaming_preference(self, message_buffer_strs: List[str], events: Any) -> PreferencesConversation:
        t = Timings("BAML: ConversePreferences")
        response = b.stream.ConversePreferences(
            existing_preference=None,
            cal_events=json.dumps(events),
            messages=message_buffer_strs,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("purple")

        async for partial_response in response:
            if partial_response.agent_response is not None:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "partial": True,
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": partial_response.agent_response.replace("\n\n", "&NewLine; &NewLine;"),
                    }
                )

        final_response = await response.get_final_response()
        final_response.agent_response = final_response.agent_response.replace("\n\n", "&NewLine; &NewLine;")
        logger.log_baml()
        return final_response

    async def streaming_combine_text(
        self, previous_response: str, textToAppend: str, other_instruction: str
    ) -> FormattedText:
        t = Timings("BAML: CombineText")
        response = b.stream.CombineText(previous_response, textToAppend, other_instruction)
        t.print_timing("purple")

        async for partial_response in response:
            if partial_response.combinedText is not None:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "partial": True,
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": partial_response.combinedText.replace("\n\n", "&NewLine; &NewLine;"),
                    }
                )

        return await response.get_final_response()
