
enum OttoCapabilityCategory {
  FLIGHT_BOOKING
  HOTEL_BOOKING
  FLIGHT_CHANGE
  FLIGHT_CANCELLATION
  HOTEL_CANCELLATION
  COMPANY_TRAVEL_POLICY
  CALENDAR_INTEGRATION
  LOYALTY_PROGRAMS
  ENTRY_REQUIREMENTS
  OTHERS @description(#"
  Other capabilities that do not fit into the above categories.
  "#)
}

class CapabilitySuggestion {
  agent_response string?
  suggested_capability string? @description(#"One capability from the OttoCapabilities list that has not been mentioned in the chat history. Return null if all capabilities have been discussed."#)
  suggested_category OttoCapabilityCategory? @description(#"The category of the suggested capability."#)
  reason string @description(#"
    the reason why suggest this capability 
  "#)

}

function SuggestCapability(
  chat_history: string[],
  preferences: string,
  recent_suggested_capabilities: string[],
  connected_to_calendar: bool,
  self_intro: string?,
  current_action: string?,
  flight_search_count: int,
  hotel_search_count: int,
  flight_booked_count: int,
  hotel_booked_count: int,
  is_international_flight: bool,
  has_ffn: bool,
  has_company_travel_policy: bool
) -> CapabilitySuggestion {
  client GPT41
  prompt #"
  {{ ConversationHistory(chat_history, 0) }}

  {{ _.role("system")}}

  Role: Capability Analyzer
  {{ self_intro | default(GetSelfIntro()) }}

  Goal: Analyze the chat history to identify one capability that has not been mentioned or discussed.

  Procedure:
    - Review the entire conversation history to understand what capabilities have been mentioned or used
    - Compare against the available capabilities list provided
    - Consider the user's preferences and recent activity (timestamp) to prioritize which unused capability might be most relevant
    - Return exactly one capability that hasn't been discussed, or null if all have been covered

  ---
  Available Capabilities:
  {{OttoCapabilities()}}
  ---
  User Preferences:
  {{ preferences }}
  ---
  Already Suggested Capabilities:
  {{ recent_suggested_capabilities }}
  ---
  {% if connected_to_calendar %}
  User has connected to the calendar.
  {% else %}
  User has not connected to the calendar.
  {% endif %}
  ---
  User's current action: {{current_action}}
  ---
  {% if is_international_flight %}
  User is currently searching for an international flight.
  {% endif %}
  ---
  {% if flight_search_count == 0 %}
  User has not done any flight search.
  {% else %}
  User has done flight search {{ flight_search_count }} times.
  {% endif %}
  {% if hotel_search_count == 0 %}
  User has not done any hotel search.
  {% else %}
  User has done hotel search {{ hotel_search_count }} times.
  {% endif %}
  ---
  {% if flight_booked_count == 0 %}
  User has not booked any flight.
  {% else %}
  User has booked flight {{ flight_booked_count }} times.
  {% endif %}
  {% if hotel_booked_count == 0 %}
  User has not booked any hotel.
  {% else %}
  User has booked hotel {{ hotel_booked_count }} times.
  {% endif %}
  ---
  {% if has_ffn %}
  Yes, User has frequent flyer numbers on file.
  {% else %}
  No, User does not have frequent flyer numbers on file.
  {% endif %}
  ---
  {% if has_company_travel_policy %}
  Yes, User has a company travel policy on file.
  {% else %}
  No, User does not have a company travel policy on file.
  {% endif %}
  ---
  Guidelines:
    - Only suggest capabilities that are completely unmentioned in the chat history.
    - Return null if all capabilities have been adequately discussed
    - DO NOT suggest capabilities that have already suggested before.
    - If the user has already connected to the calendar, do not suggest calendar integration capabilities.
    - If the user already has frequent flyer numbers (FFN) on file, do not suggest loyalty program capabilities.
    - If the user did flight or hotel search, you can suggest connecting to the calendar.
      Making suggestion of connecting to the calendar more frequent, as it is more important feature, but don't suggest it 2 times in a row.
      But if the user is in the middle of a flight or hotel BOOKING, do not suggest connecting to the calendar.
    - If the user has done flight or hotel search, you can suggest company travel policy capabilities.
    - If the user already has a company travel policy on file, do not suggest company travel policy capabilities.
    - Please sugget company travel policy capabilities separately from loyalty programs, the category of company travel policy is COMPANY_TRAVEL_POLICY, but loyalty programs are in LOYALTY_PROGRAMS category.
    - Only suggest visa and entry requirment checks if the flight is international. When suggesting visa and entry requirement checks, please ask the user to provide the destination country and citizenship.
    - Only suggest flight change capability or flight cancellation capability if the user is in the final step (FLIGHT_BOOKING) of booking a flight. 
    - Only suggest hotel cancellation capabilities if the user is in the final step (HOTEL_BOOKING) of booking a hotel.
    - If the user has done flight search, but haven't done any hotel search before, you can suggest hotel booking capabilities.
    - But if the user has already done hotel search or hotel booking, DO NOT suggest hotel booking capabilities.
    - If the user has done hotel search, but haven't done any flight search before, you can suggest flight booking capabilities.
    - But if the user has already done flight search or flight booking, DON NOT suggest flight booking capabilities.
    - If the user is already in the middle of flight booking process, then DO NOT suggest flight booking capabilities.
    - If the user is already in hte middle of hotel booking process, then DO NOT suggest hotel booking capabilities.

  Response Format:
    - For agent response, provide a brief explanation of why the suggested capability is useful.
    - The response can be lesiurely and friendly, but should not be too long, limit to 20 ~ 25 words, otherwise user might not read it.
    - No need to set agent response if the suggested capability category is CALENDAR_INTEGRATION.
  
    -------
    Example agent response:

    --Flight booking category example--
    Manage hotels in seconds.
    Book, change, or cancel your hotel directly in chat - no apps, no calls, no stress.
    Try booking your next hotel with a quick message.

    --Hotel booking category example--
    Manage hotels in seconds.
    Book or cancel your hotel directly in chat - no apps, no calls, no stress.
    Try booking your next hotel with a quick message."

    --Loyalty programs category example--
    Earn miles on every booking.
    I'll securely store your loyalty numbers and apply them automatically.
    Add your frequent flyer numbers now.

    --Company travel policy category example--
    Stay within budget, every time.
    I'll recommend hotels that follow your company's policy - saving you time, money, and audit pain.
    Set up your company policy now."

    --Entry requirements category example--
    Be travel-ready with no surprises.
    I check visa and entry requirements based on your citizenship and destination.
    Tell me your next destination, citizenship and I'll check for you.

    --Flight cancellation category example--
    Need to cancel? I've got you.
    Skip the long hold times. I can cancel your flights right in chat.
    Let me know what changed - we'll cancel it fast.

    --Flight change category example--
    Need to change? I've got you.
    Skip the long hold times. I can change your flights right in chat.
    Let me know what changed - we'll change it fast.

    --Hotel cancellation category example--
    Need to cancel? I've got you.
    Skip the long hold times. I can cancel your hotels right in chat.
    Let me know what changed - we'll cancel it fast.

    -------

  {{ _.role("system")}}
  {{ ctx.output_format }}
  "#
}
