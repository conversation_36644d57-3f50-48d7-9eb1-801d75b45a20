test do_seat_selection_test {
  functions [DoSeatSelection]
  args {
    seat_map ""
    preferred_seat "middle"
    current_flight "DL7946: SEA to MEX"
    all_flights "DL7946: SEA to MEX, DL7947: MEX to SEA"
    next_flight "DL7947: MEX to SEA"
  }

  @@assert({{ this.seat == none }})
}


test <PERSON><PERSON><PERSON>er {
  functions [DoAvaiableSeatSelection]
  args {
    self_intro #"You are <PERSON>, a professional corporate travel consultant assisting frequent business travelers. You support busy executives with travel planning across flights, hotels, and visas."#
    convo_style #"You speak with efficiency, confidence, and experience—like someone who has booked thousands of trips and knows what matters. You recommend options clearly and proactively, and you avoid generic language like “good option” or “smooth itinerary.” Instead, explain why something works well. Your tone is polished but natural, like a trusted human assistant on the phone. Never repeat yourself or over-explain. Anticipate what the traveler needs next and move the process forward without prompting. Prioritize clarity, timing, and business convenience in every recommendation. You respond with the language the user used in the last message. If no further instruction is given, keep responses under 25 words."#
    preferred_seat #"Window"#
    seat_map #"
      ROW,<PERSON>L<PERSON><PERSON>,SEAT_TYPE,SEAT_LOCATION,IS_WING,PRICE
      8,A,WIND<PERSON>,[],False,78.31
      8,B,<PERSON>NTER,[],False,78.31
      8,E,CENTER,[],False,78.31
      10,A,WINDOW,[],False,78.31
      10,B,CENTER,[],False,78.31
      10,C,AI<PERSON>E,[],False,78.31
      10,E,CENTER,[],False,78.31
      11,A,WINDOW,[],False,78.31
      11,B,CENTER,[],False,78.31
      11,C,AISLE,[],False,78.31
      11,D,AISLE,[],False,78.31
      11,E,CENTER,[],False,78.31
      11,F,WINDOW,[],False,78.31
      12,A,WINDOW,[],False,78.31
      12,B,CENTER,[],False,78.31
      12,C,AISLE,[],False,78.31
      12,D,AISLE,[],False,78.31
      12,E,CENTER,[],False,78.31
      12,F,WINDOW,[],False,78.31
      14,A,WINDOW,[],True,78.31
      14,B,CENTER,[],True,78.31
      14,C,AISLE,[],True,78.31
      14,D,AISLE,[],True,78.31
      14,E,CENTER,[],True,78.31
      14,F,WINDOW,[],True,78.31
      20,A,WINDOW,['EXIT_ROW'],True,78.31
      20,B,CENTER,['EXIT_ROW'],True,78.31
      20,C,AISLE,['EXIT_ROW'],True,78.31
      20,D,AISLE,['EXIT_ROW'],True,78.31
      20,E,CENTER,['EXIT_ROW'],True,78.31
      20,F,WINDOW,['EXIT_ROW'],True,78.31
      21,A,WINDOW,['EXIT_ROW'],True,78.31
      21,B,CENTER,['EXIT_ROW'],True,78.31
      21,C,AISLE,['EXIT_ROW'],True,78.31
      21,E,CENTER,['EXIT_ROW'],True,78.31
      21,F,WINDOW,['EXIT_ROW'],True,78.31
      22,A,WINDOW,[],True,35.06
      22,B,CENTER,[],True,26.88
      22,C,AISLE,[],True,35.06
      23,A,WINDOW,[],True,35.06
      23,D,AISLE,[],True,35.06
      23,E,CENTER,[],True,26.88
      23,F,WINDOW,[],True,35.06
      24,A,WINDOW,[],True,35.06
      24,B,CENTER,[],True,26.88
      24,C,AISLE,[],True,35.06
      24,D,AISLE,[],True,35.06
      24,E,CENTER,[],True,26.88
      24,F,WINDOW,[],True,35.06
      25,A,WINDOW,[],False,35.06
      25,D,AISLE,[],False,35.06
      25,E,CENTER,[],False,26.88
      25,F,WINDOW,[],False,35.06
      26,A,WINDOW,[],False,35.06
      26,B,CENTER,[],False,26.88
      26,C,AISLE,[],False,35.06
      26,D,AISLE,[],False,35.06
      26,E,CENTER,[],False,26.88
      26,F,WINDOW,[],False,35.06
      27,A,WINDOW,[],False,35.06
      27,B,CENTER,[],False,0
      27,C,AISLE,[],False,35.06
      27,D,AISLE,[],False,35.06
      27,E,CENTER,[],False,0
      27,F,WINDOW,[],False,35.06
      28,A,WINDOW,[],False,35.06
      28,B,CENTER,[],False,0
      28,C,AISLE,[],False,35.06
      28,D,AISLE,[],False,35.06
      28,E,CENTER,[],False,0
      28,F,WINDOW,[],False,35.06
      29,A,WINDOW,[],False,0
      29,B,CENTER,[],False,0
      29,C,AISLE,[],False,0
      30,A,WINDOW,[],False,0
      30,B,CENTER,[],False,0
      30,C,AISLE,[],False,0
      30,D,AISLE,[],False,0
      30,E,CENTER,[],False,0
      30,F,WINDOW,[],False,0
      31,A,WINDOW,[],False,0
      31,B,CENTER,[],False,0
      31,C,AISLE,[],False,0
      31,D,AISLE,[],False,0
      31,E,CENTER,[],False,0
      31,F,WINDOW,[],False,0
      32,A,WINDOW,[],False,0
      32,B,CENTER,[],False,0
      32,C,AISLE,[],False,0
      32,D,AISLE,[],False,0
      32,E,CENTER,[],False,0
      32,F,WINDOW,[],False,0
      34,A,WINDOW,[],False,0
      34,B,CENTER,[],False,0
      34,C,AISLE,[],False,0
      34,D,AISLE,[],False,0
      34,E,CENTER,[],False,0
      34,F,WINDOW,[],False,0
      35,A,WINDOW,[],False,0
      35,B,CENTER,[],False,0
      35,C,AISLE,[],False,0
      35,D,AISLE,[],False,0
      35,E,CENTER,[],False,0
      35,F,WINDOW,[],False,0
      36,A,WINDOW,[],False,0
      36,B,CENTER,[],False,0
      36,C,AISLE,[],False,0
      36,D,AISLE,[],False,0
      36,E,CENTER,[],False,0
      36,F,WINDOW,[],False,0
      37,A,WINDOW,[],False,0
      37,B,CENTER,[],False,0
      37,C,AISLE,[],False,0
      37,D,AISLE,[],False,0
      37,E,CENTER,[],False,0
      37,F,WINDOW,[],False,0
      
    "#
    current_flight #"{"origin": {"airportCode": "SEA", "airportName": "Seattle\u2013Tacoma International Airport", "cityName": "Seattle", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles"}, "destination": {"airportCode": "SFO", "airportName": "San Francisco International Airport", "cityName": "San Francisco", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles"}, "flight_type": "Boeing 737-800", "airline_code": "UA", "airline_name": "United Airlines", "flight_number": "2355"}"#
  }
}