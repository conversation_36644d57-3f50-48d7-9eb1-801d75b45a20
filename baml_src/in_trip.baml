
class FlightChangeEvent {
  change_Severity string @description(#"The severity of the change. One of `critical`, `moderate`, `minor`."#)
  change_summary string @description(#"A short summary less than 4 words of the flight change event"#)
  agent_response string @description(#"A helpful response to the traveler includes summary of the change and resolution options. "#)
}

class HotelChangeEvent {
  change_severity string @description(#"The severity of the change. One of `critical`, `moderate`, `minor`."#)
  change_summary string @description(#"A short summary less than 4 words of the hotel change event"#)
  agent_response string @description(#"A helpful response to the traveler about the hotel change and next steps."#)
}

function ProcessFlightChangeEvent(
    existing_booking: string, 
    change_event_details: string, 
    current_date: string, 
    self_intro: string?, 
    convo_style: string?) -> FlightChangeEvent {
  client GPT4o
  prompt #"
    {{ _.role("system")}}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    Core Objective
     provided a flight change event, analyze the change against the existing booking and provide actionable resolution options for the user.

    Flight changes details (JSON format):
    {{ change_event_details }}
    ---
    Existing booking details (JSON format):
    {{ existing_booking }}
    ---

    Analysis Framework:
        Step 1: Change Detection & Impact Assessment
        Step 2: Impact Severity Classification
          Classify each change as:
          CRITICAL: Cancellations, major delays (>3 hours), missed connections
          MODERATE: Schedule changes 1-3 hours, involuntary reroute
          MINOR: Small delays (<1 hour), terminal changes, seat reassignment, gate changes, aircraft equipment change 
        Step 3: Generate Resolution Options
          Provide options based on change severity:
            For minor changes, provide a brief summary and no further action is required.
            For moderate changes and critical changes, provide suggested options for the traveler.
    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function ProcessHotelChangeEvent(
    existing_booking: string, 
    change_details: string, 
    current_date: string, 
    self_intro: string?, 
    convo_style: string?) -> HotelChangeEvent {
  client GPT4o
  prompt #"
    {{ _.role("system")}}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    Core Objective:
    Analyze a hotel booking change and provide helpful guidance to the traveler.

    Hotel change details:
    {{ change_details }}
    ---
    Existing booking details (JSON format):
    {{ existing_booking }}
    ---

    Analysis Framework:
      Step 1: Change Impact Assessment
      Step 2: Generate Suggested Options
        Provide actionable guidance for the traveler including rebooking options and support.
        if the hotel is cancelled, the suggested option is to rebook a new hotel.
        if the user is no show, tell we can check with the hotel to see if they can help with the refund.
      Step 3: Response Formatting
        Offer a helpful explanation of the hotel change and outline the next steps. Ask the user if they would like to proceed with the suggested options.
        If the user has any other preferences or thoughts, invite them to share.   
    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}
