enum FrontOfHouseWorkType {
  // TODO:(chengxuan) to add this back, currently it is not stable.
  // NoAction @description(#"
  //   When the user's latest message does not require any new action, planning, change, inquiry, or profile update. 
  // "#) 
  FlightPlanning @description(#"When the user is specifically talking about planning or booking a flight that's not booked yet without mentioning hotels. This includes searching for flights, selecting flights."#)
  HotelPlanning @description(#"When the user is specifically talking about planning or booking a hotel that's not booked yet without mentioning flights. This includes searching for hotels, selecting hotels."#)

  ChangeFlight @description(#"When the user wants to exchange a flight, e.g. flight class, date etc. Needs a confirmation ID, confirmation number, or PNR."#)
  ChangeFlightSeat @description(#"When the user wants to change seats for flights. Only set to this if you have a confirmation ID, confirmation number, or PNR."#)
  Cancellation @description(#"When the user wants to cancel any bookings, tickets or reservations"#)
  ProfileHandler @description(#"When the user themselves explicitly wants to update their profile information, including personal details and payment methods. The notification from them that they have upated the profile in the pop up form doesn't count."#)
  Inquiry @description(#"
    The content and intent of the traveler message is asking for explanations, clarifications, or reasoning about:
    e.g.,
    1. Why certain choices were made
    2. Questions about policies, costs, or procedures
    3. General comments about travel and destination cities or locations
    4. Messages to specify destination and travel dates
    5. Check realtime status of user already booked flight/hotel.
    6. Itinerary details
    7. Anytime If the user sends a greeting, expression of thanks, or any message not related to the active travel workflow.
    8. If the question is asking information about flight or hotel, classify as `"Inquiry"`.
    Important: If the user mentions about flight seats related tasks, such as selecting a seat, changing a seat, or checking seat availability, classify as either `"FlightPlanning"` or `"ChangeFlightSeat"` based on the context of the conversation, but not as `"Inquiry"`.
  "#)
  UpdatePreferences @description(#"
    Last traveler's message mentioned any preferences below:
    preferred_home_airport, preferred_airline_brands, preferred_cabin, preferred_seats, preferred_hotel_brands, preferred_travel_misc, preffered_name.

    preffered_name is only need to be updated when the user explicitly wants to update their name.
"#)

  InternationalInfoCheck @description(#"
    When the user wants know anything about visa, entry requirements, etc for international travel.
  "#)

  @@dynamic
}

class FrontOfHouseSupervisorResponse {
  work_types FrontOfHouseWorkType[] @description(#"The types of work the front of house supervisor should perform"#)
  // reason string @description(#"The reason for the work"#)
}

function FrontOfHouseSupervisorDoConverse(
  messages: string[], 
  current_date: string,
  travel_context: string?,
  previous_destination: string?,
  previous_trip_dates: string?,
  has_concluded_trip: bool?) -> FrontOfHouseSupervisorResponse {
  client GPT41
  prompt #"

  {{ ConversationHistory(messages, 0) }}

  Role: Front of house supervisor

  Goal: Categorize the recent conversation into FrontOfHouseWorkType(s).

  Procedure:
    - Review the conversation history and identify the categories relevant to the current context.

  Note:
    - Treat traveler and user as the same role.
    - A flight or hotel is only considered booked if you see a confirmation ID or number.
    - Canceling a selection or confirmation of a flight is still flight planning because it doesn't have a confirmation ID or number.
    - Canceling a selection or confirmation of a hotel is still hotel planning because it doesn't have a confirmation ID or number.
    - Checking flight seat availability is still flight planning, so it is not classified as ChangeFlightSeat, but as FlightPlanning.
    - Changing flight seat for **unbooked** flight (without confirmation ID or number) is still flight planning.
    - Changing flight seat (NOT seat preference) for **booked** flight (with confirmation ID or number) is classified as ChangeFlightSeat.
    - If the user is specifically talking about flights (searching, booking, preferences) without mentioning hotels, classify as FlightPlanning.
    - If the user is specifically talking about hotels (searching, booking, preferences) without mentioning flights, classify as HotelPlanning.

  Critical Points:
    - If there is no confirmation ID, confirmation number, or PNR, changing a seat is still considered trip planning.
    - when the user want to update their name, classify as UpdatePreferences, do not include other work types.
    - When determine the work type by taking the whole conversation history into consideration, but the intent should be only determined by the latest user message.
    - If user responds to the entry requirements message which also includes the question about proceeding booking, either "yes" or "no", classify as FlightPlanning or HotelPlanning, but not as InternationalInfoCheck.
    - If the user is starting a trip, but not explicitly asking for a flight or hotel, let's start with flight planning.
    - If the assistant's last message asked if the user wants to provide or update payment information, and the user's latest message is a direct affirmative response, classify as ProfileHandler only if the user's intent is solely focused on updating their profile information. If the user's intent is to proceed with booking or planning after updating their profile, classify based on the active travel workflow (e.g., HotelPlanning or FlightPlanning).
    - If `ProfileHandler` is a relevant work type, then `work_types` should only contain `ProfileHandler` if no other active travel workflow is indicated in the user's latest message.
    - If user is replying back to seat selection during change flight, still classify as `ChangeFlight`.
    
  **Drastic Change Detection (CreateNewTrip):**
    - Check if the topic has drastically changed from previous conversation context
    - Destination Rules: If new origin airport ≠ previous destination airport AND this is not a reasonable round-trip (e.g., SEA->LAX then LAX->SEA within reasonable timeframe)
    - Time Rules: If trip dates change by >30 days from previous trip OR previous conversation was concluded (has_concluded_trip = true)
    - Explicit Intent: If the user explicitly expressed the intention of creating new trip like "Let's start over," or "Different trip now."
    - If any condition is met, classify as CreateNewTrip instead of other work types
    - Round-trip assumption: Consider it reasonable if the new origin matches the previous destination and the timeframe suggests a return journey
    - Previous context: travel_context={{ travel_context }}, previous_destination={{ previous_destination }}, previous_dates={{ previous_trip_dates }}, concluded={{ has_concluded_trip }}
    
  {{ _.role("system")}}
  {{ ctx.output_format }}
  "#
}

class FrontOfHouseTripBrief {
  title string
  start_date string? @description(#"The start date of the trip, ISO 8601 format, yyyy-mm-dd."#)
  end_date string? @description(#"The end date of the trip, ISO 8601 format, yyyy-mm-dd."#)
}

function FrontOfHouseSupervisorPostProcessing(
  travel_context: string,
  change_flight_context: string,
  ) -> FrontOfHouseTripBrief
{
  client AzureGPT4oMini
  prompt #"

  {{ _.role("system")}}

  Role: title shortener

  Goal: Update title to be more concise.

  Procedure:
    - Make sure to extract all the city names from the travel context and change flight context from start to end.
    - You could infer the city names from the travel context, such as "Seattle" for "SEA", "San Francisco" for "QSF", etc.
    - Also extract the start and end date of the trip.
    - Take change of flights, hotels into consideration, but only if the user successfully booked or submitted the change request.
    - If the user is changing a flight or hotel, make sure to update the date and city names accordingly.
    - Flight type won't change, as if it was a one-way flight, it will remain one-way and will only have one date and one city destination.
  
  Output requirment:
    - The title should be the short city name of all destinations, not including the origin or departure city.
      - For example, "LAX" -> "Los Angeles" is commonly considered the city served by the airport, and similarly "JFK" to "New York City", "FLL" to "Miami", "ORD" to "Chicago", "DFW" to "Dallas", "SFO" to "SF", "SEA" to "Seattle", "BOS" to "Boston", "PHX" to "Phoenix", and so on.
      - For international airports too, like "PGK" to "Pangkal Pinang", "TRN" to "Turin", "MDE" to Medellín, "PTP" to "Pointe-à-Pitre", and so on.
      - DO NOT use IATA airport code. Use the city that's commonly associated with the airport.
    - It's critical to keep the city name short with abbreviation like LA. The space is limited.
    - When there's more than one d≈estination city, separate them with a comma like "Seattle, SF, LA".
    - Make sure to not include the origin or departure city in the title.

  -----
  travel_context: {{ travel_context }}
  -----
  change_flight_context: {{ change_flight_context }}
  -----


  {{ _.role("system")}}
  {{ ctx.output_format }}
  "#
}
