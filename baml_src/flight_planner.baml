class FlightSelectResult {
  selected_outbound_flight_id string? @description("The selected outbound flight ID from the search results.")
  selected_return_flight_id string? @description(#"
    The selected return flight ID from the search results.
    NEVER set this field if the flight type is one way.
  "#)
  frequent_flier_number FrequentFlierNumber[]? @description(#"Has the traveler provided their frequent flyer number of the operating carrier, or the alliance programs, of the flights they want to book?"#)
  apply_flight_credits bool? @description("whether the traveler agree to apply flight credits, default is false.")
  flight_credits_ticket_numbers string[]? @description("flight credits ticket number list.")
  selected_flight_for_segment SelectedFlightForSegment[]? @description(#"
    An array of selected flights for each segment in the trip.
    Each entry should include the segment index and the selected flight ID.
    This is used to track which flight was selected for each segment in a multi-leg trip.
    Please only populate this field if the trip is a multi-leg trip.
  "#)
}

class SelectedFlightForSegment {
  segment_index int @description(#"
    The index of the segment in flight segment array the selected flight is for.
  "#)
  selected_flight_id string? @description(#"
    The selected flight ID from the search results.
    This is the id_token_key of the selected flight.
  "#)
}

class FlightSearchCoreCriteria {
  /// Core fields that are required to kick off the flight search
  departure_airport_code string? @description(#"
    A valid IATA airport or Metropolitan Area Code (MAC) code for departure. Mandatory for flight search and booking.
    If missing, prompt the traveler for the departure city or airport.
    If a city/area is given, use IATA Metropolitan Area Code (MAC) like `NYC` (New York City), `CHI` (Chicago), or `WAS` (Washington, D.C.).
    If no MAC is available, use the closest commercial airport.
  "#)
  is_departure_iata_city_code bool? @description(#"Indicates whether the departure code is an IATA MAC code rather than an airport code.
   If the value in `departure_airport_code` is a Metropolitan Area Code (MAC) like `NYC` (New York City), `CHI` (Chicago), or `WAS` (Washington, D.C.), this field **must be set to true**.
   If `departure_airport_code` is a specific airport (e.g., `JFK` for John F. Kennedy Airport), this field **must be set to false**."#)
  arrival_airport_code string? @description(#"
    A valid IATA airport or MAC code for arrival. Mandatory for flight search and booking.
    If missing, prompt the traveler for the destination city or airport.
    If a city/area is given, use IATA Metropolitan Area Code (MAC) like `WAS` (Washington, D.C.), QSF (for San Francisco Bay Area) or QLA (for Los Angeles area).
    If no MAC is available, use the closest commercial airport.
  "#)
  is_arrival_iata_city_code bool? @description(#"Indicates whether the arrival code is an IATA MAC code rather than an airport code. 
   If the value in `arrival_airport_code` is a Metropolitan Area Code (MAC) like `NYC` (New York City), `CHI` (Chicago), or `WAS` (Washington, D.C.), this field **must be set to true**.
   If `arrival_airport_code` is a specific airport (e.g., `JFK` for John F. Kennedy Airport), this field **must be set to false**."#)

  outbound_date string? @description("The departure date in ISO 8601 format (yyyy-mm-dd). Required for flight searches.")
  return_date string? @description("The return date in ISO 8601 format (yyyy-mm-dd). Required for round-trip flights; leave blank for one-way trips.")
  flight_type FlightType? @description("The type of flight the traveler is looking for. One way, round trip, or multi-legs. Only set to One way type if the user explicitly mentions the trip is one way.")
  is_international_flight_trip bool? @description(#"
    Indicates whether the trip is an international flight.
    Set to true if the trip is international, false if domestic.
    If not specified, assume the trip is domestic unless the departure and arrival airport codes are in different countries.
    For example, if the departure airport code is `LAX` (Los Angeles) and the arrival airport code is `HND` (Tokyo), set this field to true.
  "#)
  flight_segments FlightSegment[]? @description(#"
    An array of flight segments for the trip.
    Each segment should include the departure and arrival airport codes, dates, and any other relevant information.
    only populate this field if the trip is a multi-leg trip, including all segments in the order they occur.
    ONLY POPULATE THIS FIELD IF THE TRIP IS A MULTI-LEG TRIP.
  "#)
}

class FlightSegment {
  /// Core fields that are required to kick off the flight search
  departure_airport_code string? @description(#"
    A valid IATA airport or MAC code for departure. Mandatory for flight search and booking.
    If missing, prompt the traveler for the departure city or airport.
    If a city is given, use IATA Metropolitan Area Code (MAC) like `NYC` (New York City), `CHI` (Chicago), or `WAS` (Washington, D.C.), or QSF (for San Francisco Bay Area), QLA (for Los Angeles area).
    If no MAC is available, use the closest commercial airport.
  "#)
  is_departure_iata_city_code bool? @description(#"Indicates whether the departure code is an IATA MAC code rather than an airport code.
   If the value in `departure_airport_code` is a Metropolitan Area Code (MAC) like `NYC` (New York City), `CHI` (Chicago), or `WAS` (Washington, D.C.), this field **must be set to true**.
   If `departure_airport_code` is a specific airport (e.g., `JFK` for John F. Kennedy Airport), this field **must be set to false**."#)
  arrival_airport_code string? @description(#"
    A valid IATA airport or MAC code for arrival. Mandatory for flight search and booking.
    If missing, prompt the traveler for the destination city or airport.
    If a city is given, use IATA Metropolitan Area Code (MCC) like `NYC` (New York City), `CHI` (Chicago), or `WAS` (Washington, D.C.), or QSF (for San Francisco Bay Area), QLA (for Los Angeles area).
    If no MAC is available, use the closest commercial airport.
  "#)
  is_arrival_iata_city_code bool? @description(#"Indicates whether the arrival code is an IATA MAC code rather than an airport code. 
   If the value in `arrival_airport_code` is a Metropolitan Area Code (MAC) like `NYC` (New York City), `CHI` (Chicago), or `WAS` (Washington, D.C.), this field **must be set to true**.
   If `arrival_airport_code` is a specific airport (e.g., `JFK` for John F. Kennedy Airport), this field **must be set to false**."#)
  outbound_date string? @description("The departure date in ISO 8601 format (yyyy-mm-dd). Required for flight searches.")
  return_date string? @description("The return date in ISO 8601 format (yyyy-mm-dd). Required for round-trip flights; leave blank for one-way trips.")
}


template_string TimeWindowReferenceGuide() #"
    Follow this reference guide to convert vague time expressions into hour ranges (24-hour format) for flight booking:

      **Overnight/Early Hours:**
      - "midnight", "12am" → 0-1
      - "just after midnight" → 0-2
      - "after midnight" → 0-3
      - "early", "middle of the night" → 1-4
      - "pre-dawn" → 3-6

      **Morning:**
      - "very early morning" → 4-8
      - "early morning" → 5-9
      - "morning" → 7-12
      - "late morning" → 10-12

      **Midday:**
      - "midday" → 11-14
      - "noon" → 12-13
      - "lunch time" → 12-14

      **Afternoon:**
      - "afternoon" → 12-18
      - "early afternoon" → 12-15
      - "late afternoon" → 15-18

      **Evening:**
      - "dinner time" → 17-20
      - "evening" → 18-22

      **Night:**
      - "night" → 21-24
      - "late night" → 22-24

      **Business Travel Times:**
      - "business hours" → 8-18
      - "before work" → 6-9
      - "after work" → 18-21

      **Time Constraints:**
      - "depart by [time]" → [hour]-23 (extract hour, departure from that hour until end of day)
      - "arrive by [time]" → 0-[hour]  (extract hour, arrival from start of day until that hour)
      - "meeting at [time]" → 0-[hour]  (extract hour, arrival before meeting time)
      Note: Extract only the hour portion from any time format (9:30, 14:45, 3:20pm, etc.)

    For instance, expressions like “I want a morning flight” are interpreted as the time range 7–12, while “depart by 9am” translates to the range 0–9.
    If no clear time preference is given, leave the field empty.
"#

class FlightSearchAdditionalCriteria {
  ignore_all_airlines bool? @description(#"
    Indicates whether the traveler wants to ignore all airlines, including their preferred airlines and popular airlines in the airport.
    Set to true if the traveler explicitly states they want to ignore all airlines, false otherwise.
    If not specified, assume the traveler does not want to ignore any airlines so set to false.
  "#)
  /// Additional fields that are optional and can be used to refine the search  preferred_number_of_stops int? @description("The traveler's preferred number of stops for the flight.")
  seat_types string[]? @description(#"
    The traveler's preferred seat types from user's latest message.
    If not explictly stated by the traveler in chat history, use the traveler's preference: specifically from the traveler's preferences: preferred_seats.
    The value can be for example: ['window', 'aisle', 'middle', 'front of plane', 'middle of plane', 'any seat'] etc. any seat (or 'no preference' or 'no' or any other similar words)  means traveler prefer any seats
    If the traveler don't want to provide the preferred seat types, please set this field to any seat.
    if there is no seat preference in traveler user preference, it doesn't mean the traveler has no preference, ONLY SET TO 'any seat' if the traveler EXPLICITLY states they have no preference.
  "#)
  // why_seat_types string @description(#"
  //   The reason why the traveler chose the seat types for this trip.
  // "#) 
  preferred_airline_codes string[]? @description(#"
    An array of the traveler's preferred airline brand in IATA codes format.
    Look for the airline in chat history first,
    If not explictly stated by the traveler in chat history, use the traveler's preference: specifically from the traveler's preferences: preferred_airline_brands.
    Example: ["F9", "UA", "DL"]
    Always convert airline names to IATA codes (e.g., "Frontier" → "F9").
  "#)
  // why_preferred_airline_codes_is_null string
  // @description("The reason why the traveler chose the airline brands for the flight search. but I need IATA format")
  cabin string[]? @description(#"The cabin preference for this trip. If not specified use the preference. Must be one of the four: economy, premium_economy, business, first)."#)
  outbound_departure_time string? @description(#"
    A string containing two dash-separated numbers (e.g., '10-15') expressed a **preferred time of day for outbound flight departure**.
    If no clear time preference is given, leave the field empty.
  "#)
  outbound_arrival_time string? @description(#"
    A string containing two dash-separated numbers (e.g., '10-15') expressed a **preferred time of day for outbound flight arrival**.
    If no clear time preference is given, leave the field empty.
  "#)
  return_departure_time string? @description(#"
    A string containing two dash-separated numbers (e.g., '10-15') expressed a **preferred time of day for return flight departure**.
    If no clear time preference is given, leave the field empty.
  "#)
  return_arrival_time string? @description(#"
    A string containing two dash-separated numbers (e.g., '10-15') expressed a **preferred time of day for return flight arrival**.
    If no clear time preference is given, leave the field empty.
  "#)
  number_of_stops int? @description("The maximum number of stops the traveler is willing to make during the flight.")
  refundable bool? @description(#"
    Indicates whether the traveler prefers refundable tickets.
    Set to true if the traveler explicitly states they want refundable tickets, false otherwise.
    If not specified, assume the traveler does not want refundable tickets.
  "#)

  upgrade bool? @description(#"
    Indicates whether the traveler is looking for can be upgraded flight options.
    Set to true if the traveler explicitly states they want upgradable options, false otherwise.
    If not specified, assume the traveler does not want upgradable options.
  "#)
  
  price_range string? @description(#"
    The price range for the flight search.
    If not specified, assume no price range.
    The price range should be expressed as a string in the format 'min-max' (e.g., '100-300').
    Always integer.
    Note that while we collect a range, only the maximum price will be used for flight search API calls.
  "#)
  
  other_not_categorized_preferences string[]? @description("Other flight preferences that are not categorized in the above fields.")
}

enum FlightPlanningStep {
  NONE @description(#"
    This is the default value, Set to this when can't apply any other values.
  "#)
  TRIP_DETAIL_NOT_COLLECTED @description(#"The user has not provided the necessary core flight parameters: departure airport code (or city), arrival airport code (or city), and departure date. For round-trip flights, the return date is also required."#)
  OUTBOUND_FLIGHT_SEARCH
  RETURN_FLIGHT_SEARCH

  FLIGHT_SEARCH @description(#"
    only set to this when you want to do flight search for MULTI_LEGS flight, don't set to this when you are doing flight search for one way or round trip flight.
    no matter which segment you are searching for.
  "#)

  FLIGHT_CHECKOUT
  FLIGHT_BAGGAGE_CHECK
  FLIGHT_VALIDATION
  FLIGHT_CREDIT_CHECK
  FLIGHT_BOOKING
  CHECK_SEAT_AVAILABILITY @description(#"
    set to this step if the traveler wants to check seat availability/seat map for a specific flight, provide it based on the stop, the stop is determined by the start and end airport.
  "#)
}

class FlightPlanResponse {
  updated_flight_search_core_criteria FlightSearchCoreCriteria? @description(#"
      The delta update for flight search core criteria.

      - **Only include fields that have changed** compared to the previous state.
      - **Exclude** fields that are `null`, `none`, or unchanged to reduce latency.
  "#)
  updated_flight_search_additional_criteria FlightSearchAdditionalCriteria? @description(#"
      The delta update for flight search additional criteria.

      - **Only include fields that have changed** compared to the previous state.
      - **Exclude** fields that are `null`, `none`, or unchanged to reduce latency.
  "#)
  seat_selections SeatSelectionForFlight[]? @description(#"
    An array of selected seat for each flight in the trip so far.
  "#)  
  updated_flight_select_result FlightSelectResult? @description("The delta update for the selected flight. **Only include fields that have changed. Exclude `null` or `none` values.**")
  updated_ctizenship_info string[]? @description(#"
      The user's citizenship countries. it might be a list of countries if the user has multiple citizenships.
      This should be a 2-letter ISO 3166-1 alpha-2 country code (e.g., 'US' for United States, 'UK' for United Kingdom, 'JP' for Japan).
  "#)
  current_searching_segment_index int? @description(#"
      The index of the segment in flight_segments array that is currently being searched. 
      Only set this when searching for a multi-legs flight and MUST BE SET.
      the first segment is at index 0, please always set this field when searching for a multi-legs flight.
  "#)
  current_step FlightPlanningStep @description(#"
      Indicates the current step in the flight planning process.

      - Set to `OUTBOUND_FLIGHT_SEARCH` or `RETURN_FLIGHT_SEARCH` **only when** all required flight search core criteria are VALID.
      - **DO NOT** set to `RETURN_FLIGHT_SEARCH` if the flight type is one way.
      - **DO NOT** set to `OUTBOUND_FLIGHT_SEARCH` or ``RETURN_FLIGHT_SEARCH` if the flight type is multi-legs. Only set to `FLIGHT_SEARCH` in this case.
      - Set to `RETURN_FLIGHT_SEARCH` **after** the outbound flight has been selected for flight type is round trip.
      - **NEVER** set both `OUTBOUND_FLIGHT_SEARCH` and `RETURN_FLIGHT_SEARCH` at the same time. For round-trip flights, set `RETURN_FLIGHT_SEARCH` **only after** the outbound flight has been selected.
  "#)
  // why_current_step string? @description(#"
  //      The reason why the current step is set to this value.
  // "#)
  user_responsed_entry_requirment bool? @description(#"
    whether the user has responded to checking official country entry requirment request: such as visas, passports, ESTA etc.
    - true: - Only set user_responsed_entry_requirment to true when the user has been prompted and responded regarding immigration or visa requirements (like passports, visas, ESTA).
    - false: the user has not responded to the entry requirment request.
    If the value is true, please make sure include it in the json response.
    
    Note: Decisions about flight credits, seat selection, or payment methods are unrelated and should not affect this field.    
  "#)
  availability_param CheckSeatAvailability?
  // why_user_responsed_entry_requirment_is_true string? @description(#"
  //   The reason why the user_responsed_entry_requirment is set to true.
  //   This should be a string that explains the reason.
  // "#)
  agent_response string @description(#"
      A professional and structured response for the user, designed to confirm progress and request any missing details.

      1. **Acknowledge the request**
        - Start with a clear confirmation like 'Got it' or 'Sure'.
      2. **Summarize the current flight plan**
        - Include all collected flight details so far (departure, destination, dates, flight preferences).
      3. **Ask for missing core or additional criteria**
        - If core criteria (e.g., flight departure/arrival airports, travel dates) are incomplete, request them explicitly.
        - For round-trip flights, confirm whether the user wants a return flight if the return date is missing.
        - For core criteria, only ask for the missing ones.
      4. **Notify when a search starts**
        - If all core criteria for a flight search are met, inform the user that the search has started in the background.
        - Disregard any word limits for this message. output all the flight search criteria in a structured format with bullet points. including core and additional criteria, and their relavant travel preferences.
      5. **Acknowledge selections**
        - When the user selects a flight, confirm the selection by repeating the chosen details (e.g., "You selected the return flight on Alaska Airlines departing Los Angeles (LAX) at 4:00 PM.").
        - Excepton: If user are providing seat preference with an existing selected flight, just summarize the seat preference, **do not provide flight details again**.
      6. **Avoid redundant searches**
        - If results already exist, do not suggest starting a new search unless explicitly requested.
      7. **Validate selections before booking**
        - If the selection needs validation, inform the user that final confirmation is in progress before proceeding with booking.
        - DO NOT display the flight details again when user confirm the flight selection. Keep it short and simple.
  "#)
}

template_string Metropolitan_Area_Code_Instruction #"
              If a city or metropolitan area is mentioned without specifying a particular airport, use the appropriate IATA Metropolitan Area Code (MAC) to represent the region.
              {{ MAC_CODE() }}
              Always infer user intent from context. If the user specifies a city and there is a corresponding MAC, use the MAC code.
"#

template_string MAC_CODE #"
Valid MAC codes include:
  - NYC (New York City): JFK, EWR, LGA
  - CHI (Chicago): ORD, MDW
  - WAS (Washington, D.C.): IAD, DCA, BWI
  - QSF (San Francisco Bay Area): SFO, OAK, SJC
  - QLA (Los Angeles Area): LAX, ONT, SNA, BUR
  - QMI (Greater Miami Area): MIA, FLL, PBI
  - DFW (Dallas-Fort Worth): DFW, DAL
  - MIA (South Florida Area): MIA, FLL
  - YTO (Toronto): YYZ, YTZ
  - YMQ (Montreal): YUL, YMY
  - YVR (Vancouver): YVR, YXX
  - DTT (Detroit): DTW, YIP
  - QHO (Houston Area): IAH, HOU
  - BJS (Beijing): PEK, PKX
  - TYO (Tokyo): NRT, HND
  - SEL (Seoul): ICN, GMP
  - JKT (Jakarta): CGK, HLP
  - OSA (Osaka): KIX, ITM
  - SPK (Sapporo): CTS, OKD
  - MOW (Moscow): SVO, DME, VKO
  - PAR (Paris): CDG, ORY, LBG
  - LON (London): LHR, LGW, STN, LTN, LCY, SEN, BQH, QQS
  - ROM (Rome): FCO, CIA
  - MIL (Milan): MXP, LIN
  - STO (Stockholm): ARN, NYO, BMA
  - BUH (Bucharest): OTP, BBU
  - BER (Berlin): BER
  - EAP (Basel-Mulhouse-Freiburg): BSL, MLH
  - MEX (Mexico City): MEX, TLC, NLU
  - BUE (Buenos Aires): EZE, AEP
  - RIO (Rio de Janeiro): GIG, SDU
  - SAO (São Paulo): GRU, CGH, VCP
"#

// ---- FlightPlanner with Serp ----
function FlightPlanner(
  travel_preference: string,
  flight_search_core_criteria: string?,
  flight_search_additional_criteria: string?,
  messages: string[],
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  flight_select_result: string?,
  has_credit: bool,
  pay_baggage: string?,
  user_name: string?,
  
  ) -> FlightPlanResponse {
  client GPT41
  prompt #"

    {{ ConversationHistory(messages, 0) }}

    {{ _.role('system') }}
    Role: Executive Travel Assistant - Flight Specialist

    Background:
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}
    - Today's date is {{ current_date }}. All dates should be in the future, unless explicitly stated otherwise.
    - Process dates intelligently: infer partial dates (e.g., "next Friday"), validate all dates against today's date, and inform the user of any issues.
    - {{RespondWithName(user_name)}}

    Key Guidelines:
    1. Search Criteria Management:
       - Collect all CORE criteria before initiating any search. The following core criteria are critical:
         - departure location: Either the departure airport code or the departure city code (only one is required).
            - When the user requests a flight and does not specify a departure airport or city, you MUST use the traveler's stored preferred home airport as the default departure airport code for the search.
            - Only override this if the user clearly states a different departure city or airport in their request.
            - If the preferred home airport is not available in user preferences and the user does not specify a departure location, prompt the user directly for their departure city or airport.
         - arrival location: Either the arrival airport code or the arrival city code (only one is required).
         - An IATA airport code is exactly three letters (e.g. SEA, JFK).
         - A city code (Metropolitan Area Code) is also three letters but covers multiple airports (e.g. NYC, CHI).
           - {{ Metropolitan_Area_Code_Instruction() }}
         - If the user provides anything else (e.g. "DE" or "ES"), prompt for clarification.
         - departure date
         - return date (only for round-trip flights)
       - Gather search criteria based on the latest user message and their stored preferences.
       - When extracting arrival or departure codes:
         - If the user clearly specifies an airport code (e.g., 'LAX'), always use the specific airport, do not convert it to a MAC code.
         - If the user clearly specifies a metropolitan area (e.g., 'Los Angeles area', 'any airport in LA'), use the corresponding MAC code (e.g., 'QLA').
       - Only when all valid core criteria are available, set current_step to the appropriate search type, otherwise set to TRIP_DETAIL_NOT_COLLECTED.
       - For flights: departure/arrival airports and dates are critical
       - If a flights selection is actively being validated or booked (as indicated by `flight_select_result` and conversation history), AND the user's latest message is a refusal to provide critical information  (e.g., payment details when requested for this selection) or the user choose close the pop up without saving it, set `current_step` to `NONE`.
       - Focus on collecting criteria for flights. Don't get distracted by other things like hotel. If the user mentions hotel criteria, do not apply these to the flight search. For example, if the user requests a refundable hotel, do not require a refundable flight unless the user specifically says so for the flight.
       - When the user requests a flight and does not specify a departure airport or city, you MUST use the traveler's stored preferred home airport as the default departure airport code for the search.
         Only override this if the user clearly states a different departure city or airport in their request.
         If the preferred home airport is not available in user preferences and the user does not specify a departure location, prompt the user directly for their departure city or airport.
       

    2. Workflow Management:
       - Focus only on flight planning tasks
       - For round trips, start return flight search only after outbound flight selection
       - After flight are searched and selected, set current_step to FLIGHT_CHECKOUT, in this step, you will do flight checkout for the selected flight: display the flight seats for user to choose and ask the user to confirm the flight seat.
          - The user can select round-trip flights in two possible ways, determine which flow is occurring based on the conversation context:
            a.Separate Selection:
              •	The user first selects an outbound flight → capture the outbound flight ID.
              •	Then the user selects a return flight → capture the return flight ID.
              •	After both selections are made, set current_step to FLIGHT_CHECKOUT.
            b. Combo Selection:
              •	The user selects a round-trip combo directly from the provided options.
              •	Based on the selected combo, extract both the outbound flight ID and the return flight ID, then set current_step to FLIGHT_CHECKOUT and proceed with seat selection.
          - For both cases, once the flight selection is complete, set current_step to FLIGHT_CHECKOUT, and proceed with seat selection for the user.
          - Note that the system will check seat type prefence, frequent flyer number itself, so you DO NOT mention, prompt, or ask the user about seat preference or frequent flyer number at any point in the response, regardless of the context.
          - when user replies back, you will still set the current_step to FLIGHT_CHECKOUT in order to do flight checkout process.
          - After user selected flight (outbound only for one way, both outbound and return roundtrip, all segments for multi legs trips), start flight checkout process in the background, set current_step to FLIGHT_CHECKOUT.
       {% if pay_baggage == None or pay_baggage ==  'ALWAYS' or pay_baggage == 'NONE' %}
       - After flight checkout (basically after seats are selected), set current_step to FLIGHT_BAGGAGE_CHECK, you will do baggage check for the selected flight.
       {% endif %}
       - After flight checkout and user selected seats (e.g. paid seat, free, 7D , no need seats and any other replies related to seat selection), set current_step to FLIGHT_VALIDATION (The user might not want any seats, that is also ok, also go to flight validation step), Always validate selections before proceeding to booking.
       {% if has_credit %}
       - After the user confirms the flight validation (using phrases like "confirm", "yes", "looks good", "book it", "go ahead" and other similar confirmative words etc.), IMMEDIATELY proceed to FLIGHT_CREDIT_CHECK as the next step.
       - Only proceed to FLIGHT_BOOKING after the flight credit check is completed or if the user explicitly declines to use flight credits.
       - Always treat a user confirmation in the validation step as an explicit trigger to move forward in the workflow.         
       {% else %}
       - After the flight validation step, if the user replies with any clear affirmative response—such as 'yes', 'confirm', 'book it', 'go ahead' or ANY OTHER SIMILAR CONFIRMATIONS—immediately transition to FLIGHT_BOOKING. Do not require additional confirmation or remain at FLIGHT_VALIDATION. Treat all such responses as explicit approval to proceed with booking.
       {% endif %}
       - If the user decline to confirm the flight, set current_step to NONE, basically do not do anything.

    3. User Communication:
       - Present the current flight plan to the user at each step
       - Clearly indicate when searches are running in the background
       - When prompting about search criteria, use terms like "trip details" or "search details"
       - If the user is telling you the price is wrong, or give you a new price, or asking you to book at a different price, you should tell them that the price is real-time and from trusted sources and you are confident about it. You should also tell them that you cannot book at a price other than the price marked in the options you offered. You will validate and confirm the final price after the traveler selected a flight right before booking.
       - If current_step is null, try to prompt the user to make a selection.
      - If the user asks to view the itinerary or flight details again, you should **ONLY** display the flight information (do not include hotels), and set current_step to NONE.
      - Don't say "Your flight reservation/selection is on hold ...". Since we do not hold flights, you shouldn't say that in any circumstances.

    Critical Requirements:
    - Only include fields that have changed in your response; exclude null/none values. Except for current_step and agent_response, which should always be included.
    - For airports: Use IATA codes (e.g., SEA, JFK) or Metropolitan City Codes (e.g., NYC, CHI, WAS)
    - For airlines: Always use IATA codes (e.g., UA for United, DL for Delta)
    - For cabin class: Use 'economy', 'premium_economy', 'business', 'first'
    - When flight search core criteria is missing, set current_step to TRIP_DETAIL_NOT_COLLECTED. Provide a clear explanation specifying exactly which core criteria are missing.
    - If flight search core criteria is invalid, for example the origin and destination airports are the same or belong to the same city, or the departure date is in the past, set current_step to TRIP_DETAIL_NOT_COLLECTED too. Also provide a clear explanation specifying exactly which core criteria are invalid.
    - If flight search core criteria is complete, and you mention that you are starting the search flights in agent_response, set current_step to OUTBOUND_FLIGHT_SEARCH or RETURN_FLIGHT_SEARCH or FLIGHT_SEARCH.
    - Use traveler's stored preferences for home airport and airline brands when not specified
    - If you don't know the departure or arrival airport, ask for the city or location, don't assume
    - After user provides seat preference: (e.g. window, any, aisle), you should set the current step to FLIGHT_CHECKOUT, as this step will do the seat selection for the user as well.
    - Do not set the current step to FLIGHT_BOOKING if FLIGHT_VALIDATION is not done.
    - Never set current_step to FLIGHT_BOOKING after a booking is confirmed (i.e., when the airline confirmation number has been issued and there are no further booking tasks). At that point, always set current_step to NONE. Only use FLIGHT_BOOKING during the booking process, never after completion.
    - If the user want to change return date after the outbound flight is selected, you should tell the user that we will need to do the whole flight search from beginning again.
    - If flight type is changed, for example from one way to round trip, you need to do the whole flight search from beginning even though flight is selected. for example, if the user want to change the flight type from one way to round trip, you should start from outbound flight search again, even though the outbound flight is selected, don't set the current step to RETURN_FLIGHT_SEARCH.
    - If the user re-select another outbound flight and user want round trip flight, you should restart the return flight search, even though the return flight is selected before.
    - If the user does not want to provide seat preference, you should assume the user want to select any seat, setting the seat_types to ['any seat'].
    - Only set to any seat for seat_types if the user explicitly says they want to select any seat or explicitly says they don't want to provide seat preference.
    - If the user wants multi-leg flights, you must need to search leg flight separately, and start first leg search, not search all segments together. after user selects the first leg flight, you should set current_step to FLIGHT_SEARCH, and then search the next leg flight.
    - When you do the flight search for segments, please set the current step to FLIGHT_SEARCH, also set current_searching_segment_index to correct index.
    - MANDATORY: Extract all time-of-day expressions from user messages that indicate a desired flight departure or arrival time — such as "early", "late", "morning", "afternoon", "evening", "by 9am", "after dinner", etc. These are critical for accurate search results.
      {{ TimeWindowReferenceGuide() }}   
    - As long as user responds to entry requirment, you should set entry_requirment_request_is_responsed to true.
    - If the user requests to change the cabin class (economy, premium economy, business, or first) for their flight after a flight has been selected, you MUST initiate a new flight search with the updated cabin class.
    - If you present round trip flight combo options to the user, and user want to select a round trip combo flight by saying: e.g.
      - "I want to book the round trip flight from SEA to AMS on May 18, 2025, and return on May 25, 2025."
      - "I want first option", 
      - "First option looks fine"
      - and other semantically similar phrases etc.
      you MUST extract both the outbound flight ID and the return flight ID from the selected combo.
    - Anytime If the user sends a greeting, expression of thanks, or any message not related to the active travel workflow, always set current_step to NONE.
    - If the user is asking questions (e.g. price, flight depature time, which seat is selected.), you should tell them that the data is real-time and from trusted sources and you are confident about it. And set current_step to NONE. But if user is asking for different fare options, you should start flight search.

    ----
    {{ _.role('system') }}
    ----
    Traveler preferences:
    {{ travel_preference }}
    ----
    Existing flight search criteria:
    flight_search_core_criteria: {{ flight_search_core_criteria }}
    flight_search_additional_criteria: {{ flight_search_additional_criteria }}
    ----
    Existing flight select result:
    flight_select_result: {{ flight_select_result }}
    ----
    
    
    Examples:
    ----
    1. Itinerary Example:
    ```markdown
    **Outbound flight:**  
    1. Airline: KLM  
    2. Flight number: KL1517  
    3. Departure: AMS at 14:15 on May 18, 2025  
    4. Arrival: BCN at 16:25  
    5. Cabin: Economy  
    6. Seat: 13C (aisle)  
    7. Fare option: Economy Standard  
    8. Cancellation policy: Non-refundable  
    9. Exchange policy: Change allowed for $78  
    **Total price: $734.70**
    ```
    ----

    {{ _.role("system")}}
    Extract the following data:
    1. Include ONLY values that have changed from existing criteria/results, except for the current_step and agent_response. Always generate current_step and agent_response.
    2. Include fields that were null or not exist in the existing search core/additional criteria but should be updated to have new value in your response.
    3. For reset values: use appropriate default (empty string for string type, empty array for array type)
    4. EXCLUDE all null fields completely from the response
    5. Ensure delta updates are minimal to reduce latency
    {{ ctx.output_format }}
  "#
}


function TripPlanValidationSummary(
  current_date: string, 
  self_intro: string,
  convo_style: string,
  seat_selections: string,
  selected_outbound_flight: string,
  matched_outbound_flight: string,
  selected_return_flight: string?,
  matched_return_flight: string?,
  original_total_price: float,
  matched_total_price: float,
  seat_price: float,
  is_employee: bool
  ) -> string {

  client AzureGPT4oMini
  prompt #"
    {{ _.role('system') }}
    Role: Executive Assistant specialized in Travel Planning

    Background:
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}
    - Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.


    Provide a summary of the flight.

    ----
    original_selected_outbound_flight: {{ selected_outbound_flight }}
    matched_outbound_flight: {{ matched_outbound_flight }}
    original_selected_return_flight: {{ selected_return_flight | default("") }}
    matched_return_flight: {{ matched_return_flight | default("") }}
    seat_selections: {{ seat_selections }}
    original_flight_price: {{ original_total_price }}
    final_flight_price: {{ matched_total_price }}
    paid_seat_price: {{ seat_price }}
    total_price: {{ matched_total_price + seat_price }}
    ----

    {{ _.role("system")}}
    Please respond with the following format:
    1. Please display matched selected outbound flight details, if there is difference between original selected outbound flight and matched outbound flight, plese mention it.
      - Don't mention it is matched flight, just say it is flight.
    2. same as 1 but for return flight, if there is no return flight, just don't display this.
    3. If there is no seat selection, just say this flight doesn't provide free seat selection.
    4. the fare option name, cancellation and exchange policy.
    5. flight price. if there is difference between original flight price and final flight price, plese mention it. e.g. The price has increased/decreased by $X to a final price of $Y.
    6. Display: "Please confirm if you would like to proceed with the booking."{% if is_employee %} followed by " - this will be booked using your company's form of payment we have on file"{% endif %}

    ----
    Notes:
    1. If the cancellation policy is not available for original selected flight, you can assume it as non-refundable before comparison.
    2. Airline name, cabin name, cancellation policy name and exchange policy name are case insensitive, and no need to be exact match: e.g. Alakska is the same as "Alaska Airlines, Inc.", "first" is the same as "First Class" or "First".
    3. No need to compare time format, just compare the value.
    4. If the seat price is not available or 0, you do not need to show it in the response, but if the seat price is available, you MUST need to show it in the response.
    ----
    
    The example output is as follows:
    ----
    Here's the full trip:
    - **Outbound:** SEA > AMS > CPH on DL 1628, Seat 14A (window).
      1. Departure: SEA at 10:15 AM on May 18  
      2. Arrival: CPH at 8:30 AM on May 19  
    - **Return:** CPH > LAX > SEA on DL 455, Seat 28C (aisle).
      1. Departure: CPH at 1:45 PM on May 25  
      2. Arrival: SEA at 6:20 PM on May 25  
    - **Fare:** Economy Standard
    - **Cancellation:** Non-refundable
    - **Change Policy:** Free change, fare difference applies
    - **Flight Price:** $686.96 USD (price has gone up $9.96)
    - **Paid Seat:** $9.96 USD.
    - **Total Price: $696.92 USD.**
    ----
  "#
}

function TripPlanValidationSummaryForMultileg(
  current_date: string, 
  self_intro: string?,
  convo_style: string?,
  seat_selections: string,
  all_flight_selection: string,
  total_price: float?,
  seat_price: float,
  is_employee: bool
  ) -> string {

  client AzureGPT4oMini
  prompt #"
    {{ _.role('system') }}
    Role: Executive Assistant specialized in Travel Planning

    Background:
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}
    - Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.


    Provide a summary of this multi leg flights. Please keep it short and simple, but informative.

    ----
    all_flight_selection: {{ all_flight_selection }}
    seat_selections: {{ seat_selections }}
    flight_price: {{ total_price }}
    paid_seat_price: {{ seat_price }}
    total_price: {{ total_price + seat_price }}
    ----

    {{ _.role("system")}}
    Please respond with the following format:
    1. Please display flight details.
    2. If there is no seat selection, just say this flight doesn't provide free seat selection.
    3. the fare option name, cancellation and exchange policy.
    4. total price. if there is difference between original total price and final total price, plese mention it. e.g. The price has increased/decreased by $X to a final price of $Y.
    5. Display: "Please confirm if you would like to proceed with the booking."{% if is_employee %} followed by " - this will be booked using your company's form of payment we have on file"{% endif %}
    ----
    
    The example output is as follows:
    If the seat price is not available or 0, you do not need to show it in the response, but if the seat price is available, you MUST need to show it in the response.
    ----
    Here's the full trip:
    - **First Leg:** SEA > AMS > CPH on DL 1628. Seat 14C (aisle).
      1. Departure: SEA at 10:15 AM on May 18  
      2. Arrival: CPH at 8:30 AM on May 19  
    - **Second Leg:** CPH > LAX > SEA on DL 455. Seat 28D (aisle).
      1. Departure: CPH at 1:45 PM on May 25  
      2. Arrival: SEA at 6:20 PM on May 25  
    - **Fare:** Economy Standard
    - **Cancellation:** Non-refundable
    - **Change Policy:** Free change, fare difference applies
    - **Flight Price:** $686.96 USD (price has gone up $9.96)
    - **Paid Seat:** $9.96 USD.
    - **Total Price: $696.92 USD.**
    ----
  "#
}

class CheckSeatAvailability {
  start_airport string
  end_airport string
}

class SeatSelectionForFlight {
  start_airport string
  end_airport string
  seat_number string @description(#"
      The seat number selected by the traveler.
      If the traveler wants to unselect the seat, set this field to `null`.
  "#)
  price float? @description(#"
      The price of the selected seat.
  "#)
}
