from enum import Enum
from typing import List, Optional

from pydantic import BaseModel

from server.utils.logger import logger
from virtual_travel_agent.helpers import console_masks

booking_log_mask = console_masks["booking.com"]


class BookingStatus(Enum):
    BOOKED = "booked"
    CANCELLED = "cancelled"
    CANCELLED_BY_ACCOMMODATION = "cancelled_by_accommodation"
    CANCELLED_BY_GUEST = "cancelled_by_guest"
    NO_SHOW = "no_show"
    STAYED = "stayed"
    UNKNOWN = "unknown"


class OrderPrice(BaseModel):
    total: float
    currency: str
    base: Optional[float] = None
    taxes: Optional[float] = None
    fees: Optional[float] = None
    accommodation_currency: Optional[float] = None
    booker_currency: Optional[float] = None


class CancellationPolicy(BaseModel):
    start: str
    end: str
    amount: float
    currency: str


class BookingDetails(BaseModel):
    order_id: str
    status: BookingStatus
    check_in: str
    check_out: str
    property_id: int
    property_name: str
    price: OrderPrice
    cancellation_policies: List[CancellationPolicy]
    guest_name: str
    guest_email: str
    created_at: str
    is_cancellable: bool = False
    error_message: Optional[str] = None
    room_name: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    hotel_email: Optional[str] = None
    remarks: Optional[str] = None
    stay_probability: Optional[float] = None
    pin_code: Optional[str] = None

    @classmethod
    def from_api_response(cls, response_data: dict) -> "BookingDetails":
        """
        Create an BookingDetails instance from the API response.

        Args:
            response_data (dict): Raw API response data

        Returns:
            BookingDetails: Structured booking details
        """
        try:
            data_slice = response_data.get("data", [])
            data = {}
            if len(data_slice) > 0:
                data = data_slice[0]

            # Convert string status to enum
            status = BookingStatus(data.get("status", "unknown"))
            if status == BookingStatus.UNKNOWN:
                logger.error(f"Unknown order status: {data}", mask=booking_log_mask)
                return cls(
                    order_id="",
                    status=BookingStatus.UNKNOWN,
                    check_in="",
                    check_out="",
                    property_id=0,
                    property_name="",
                    price=OrderPrice(total=0, currency="USD"),
                    cancellation_policies=[],
                    guest_name="",
                    guest_email="",
                    created_at="",
                    error_message=data.get("error_message"),
                )

            # Parse dates
            check_in = data.get("checkin", "")
            check_out = data.get("checkout", "")

            # Get accommodation details
            accommodation = data.get("accommodation_details", {})

            # Get first product (room) details
            product = data.get("products", [{}])[0]
            guest = product.get("guests", [{}])[0] if product.get("guests") else {}

            # Create price object with new currency structure
            price_data = product.get("price", {})
            total_price = price_data.get("total", {})
            base_price = price_data.get("base", {})

            price = OrderPrice(
                total=float(total_price.get("booker_currency", 0)),
                currency=data.get("currency", {}).get("booker", "USD"),
                base=float(base_price.get("booker_currency", 0)),
                accommodation_currency=float(total_price.get("accommodation_currency", 0)),
                booker_currency=float(total_price.get("booker_currency", 0)),
            )

            # Parse cancellation policies
            cancellation_policies = []
            policy = product.get("policies", {}).get("cancellation", [{}])[0]
            if policy:
                policy_price = policy.get("price", {})
                # Since 'from' is null in the sample, using a default start time
                cancellation_policies.append(
                    CancellationPolicy(
                        start=check_in,  # Default to check-in date since 'from' is null
                        end=check_in,  # Default to check-in date
                        amount=float(policy_price.get("booker_currency", 0)),
                        currency=data.get("currency", {}).get("booker", "USD"),
                    )
                )

            return cls(
                order_id=str(data.get("id", "")),
                status=status,
                check_in=check_in,
                check_out=check_out,
                property_id=data.get("accommodation", 0),
                property_name=accommodation.get("name", ""),
                price=price,
                cancellation_policies=cancellation_policies,
                guest_name=guest.get("name", ""),
                guest_email=guest.get("email", ""),
                created_at="",  # Not provided in the payload
                is_cancellable=True if data.get("cancellation_details") else False,
                room_name=product.get("room_details", {}).get("name"),
                address=accommodation.get("location", {}).get("address"),
                phone=accommodation.get("telephone"),
                hotel_email=accommodation.get("email"),
                remarks=data.get("remarks"),
                stay_probability=data.get("stay_probability"),
                pin_code=data.get("pin_code"),
                error_message=data.get("error_message"),
            )

        except Exception as e:
            logger.error(f"Error parsing order details: {e}, data: {response_data}", mask=booking_log_mask)
            return cls(
                order_id="",
                status=BookingStatus.UNKNOWN,
                check_in="",
                check_out="",
                property_id=0,
                property_name="",
                price=OrderPrice(total=0, currency="USD"),
                cancellation_policies=[],
                guest_name="",
                guest_email="",
                created_at="",
                error_message=str(e),
            )


class ApiResponseStatus(Enum):
    SUCCESS = "success"
    ERROR = "error"


class CancellationResponse(BaseModel):
    status: ApiResponseStatus
    message: str
    error_code: Optional[int] = None
    details: Optional[dict] = None
