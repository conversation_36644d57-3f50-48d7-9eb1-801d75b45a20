import asyncio
import json
import random
import traceback
from enum import Enum
from math import atan2, cos, radians, sin, sqrt
from pathlib import Path
from typing import Any, List, Optional

from fastapi import HTTPException

from baml_client.types import (
    PaymentTiming,
)
from hotel_agent.booking_dot_com_models import ApiResponseStatus, BookingDetails, CancellationResponse
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.schemas.authenticate.user import User
from server.services.user_profile.payment_information import (
    get_user_profile_payment_information,
    is_user_profile_payment_information_complete,
)
from server.services.user_profile.personal_information import (
    get_user_profile_personal_information,
    is_hotel_user_profile_personal_information_complete,
)
from server.utils.async_requests import make_post_request
from server.utils.logger import logger
from server.utils.mongo_connector import hotels_data_cache_collection
from server.utils.settings import settings
from virtual_travel_agent.helpers import atime_function, console_masks
from virtual_travel_agent.timings import Timings

booking_log_mask = console_masks["booking.com"]

# Booking.com Hotel API functions

cities_cache_file_name = "data/cities_cache.txt"
constants_cache_file_name = "data/constants_cache.txt"
chains_cache_file_name = "data/chains_cache.txt"
accomodations_details_cache_file_name = "data/accomodations_details_cache.txt"
districts_cache_file_name = "data/districts_cache.txt"
landmarks_cache_file_name = "data/landmarks_cache.txt"
regions_cache_file_name = "data/regions_cache.txt"

client_side_parameter_error_status_codes = [400, 404, 409]

# These were currated by Michael Gulmann
room_positive_facility_ids = [
    1,
    2,
    3,
    6,
    11,
    13,
    23,
    25,
    46,
    74,
]

# Supported accommodation types follows this sheet:
# https://docs.google.com/spreadsheets/d/1XzJN2jf9jy3R5qbHDPqbE9BH92SNtP3QQSlIQJlSSZg/edit?gid=0#gid=0
supported_accommodation_types = [
    202,
    204,
    205,
    206,
    207,
    209,
    212,
    214,
    218,
    219,
    221,
    223,
    224,
    225,
    226,
    227,
    228,
    231,
    233,
]
accomodation_positive_facility_ids = [
    2,
    3,
    6,
    7,
    11,
    15,
    17,
    20,
    23,
    43,
    44,
    46,
    49,
    52,
    53,
    64,
    73,
    75,
    81,
    101,
    107,
    111,
    124,
    127,
    128,
    129,
    133,
    134,
    139,
    140,
    160,
    161,
    180,
    181,
    182,
    183,
    184,
    199,
    209,
    210,
    219,
    304,
    460,
]  # The accomodation facilty types we are interested in

info_not_available = "Information not available."
one_mile_in_meters = 1609
distance_from_target_location_threshold = one_mile_in_meters * 50
radius_expanding_step = one_mile_in_meters * 3
max_hotel_results = 50

prod_env = "https://demandapi.booking.com"

booking_failed_status = "error_booking_creation_failed"


class HotelBookingPaymentTiming(Enum):
    PAY_AT_THE_PROPERTY = "pay_at_the_property"
    PAY_ONLINE_LATER = "pay_online_later"
    PAY_ONLINE_NOW = "pay_online_now"

    @classmethod
    def from_baml_payment_timing(
        cls, baml_payment_timing: Optional[PaymentTiming]
    ) -> Optional["HotelBookingPaymentTiming"]:
        if baml_payment_timing is None:
            # default to pay at the property
            return cls.PAY_AT_THE_PROPERTY

        if baml_payment_timing == PaymentTiming.PAY_AT_THE_PROPERTY:
            return cls.PAY_AT_THE_PROPERTY
        elif baml_payment_timing == PaymentTiming.PAY_ONLINE_LATER:
            return cls.PAY_ONLINE_LATER
        elif baml_payment_timing == PaymentTiming.PAY_ONLINE_NOW:
            return cls.PAY_ONLINE_NOW
        else:
            logger.error(f"Invalid payment timing: {baml_payment_timing}", mask=booking_log_mask)
            return None


class BookingTools:
    # Static class variables
    cities_cache = None
    constants_cache: dict | None = None
    chains_cache = None
    details_cache = None
    districts_cache = None
    regions_cache = None
    landmarks_cache = None
    reverse_accomodation_facility_lookup_by_id = None
    reverse_room_facility_lookup_by_id = None
    reverse_districts_lookup_by_name = None
    reverse_cities_lookup_by_id = None
    headers = None

    def __init__(self, user: User | None = None):
        self.booking_api_key = settings.BOOKING_DOT_COM_API_KEY
        self.booking_affiliate_id = settings.BOOKING_DOT_COM_AFFILIATE_ID
        self.user = user

        self.headers = {
            "Content-Type": "application/json",
            "X-Affiliate-Id": f"{self.booking_affiliate_id}",
            "Authorization": f"Bearer {self.booking_api_key}",
        }
        BookingTools.headers = self.headers

    @staticmethod
    async def get_charge_type_by_id(charge_type_id: int) -> str:
        constants_dict = await BookingTools._collect_constants()

        # Find the charge type with matching id
        for charge_type in constants_dict.get("charge_types") or []:
            if charge_type["id"] == charge_type_id:
                return charge_type["name"]["en-us"]

        logger.error(f"Charge type not found: {charge_type_id}", mask=booking_log_mask)
        return ""

    @staticmethod
    async def get_review_by_score(score: float) -> str:
        constants_dict = await BookingTools._collect_constants()

        for review_score in constants_dict.get("review_scores") or []:
            if review_score["maximum_score"] >= score >= review_score["minimum_score"]:
                return review_score["name"]["en-us"]

        return ""

    @staticmethod
    async def get_accomodation_facility_name_by_id(facility_id: int) -> str | None:
        if BookingTools.reverse_accomodation_facility_lookup_by_id is None:
            await BookingTools._collect_constants()

        if BookingTools.reverse_accomodation_facility_lookup_by_id is None:
            return None

        return BookingTools.reverse_accomodation_facility_lookup_by_id.get(facility_id, {}).get("name", None)

    @staticmethod
    async def get_room_facility_name_by_id(facility_id: int) -> str | None:
        if BookingTools.reverse_room_facility_lookup_by_id is None:
            await BookingTools._collect_constants()

        if BookingTools.reverse_room_facility_lookup_by_id is None:
            return None

        return BookingTools.reverse_room_facility_lookup_by_id.get(facility_id, {}).get("name", None)

    @staticmethod
    async def _collect_constants() -> dict:
        logger.debug("Entering _collect_constants", mask=booking_log_mask)
        if BookingTools.constants_cache is not None:
            return BookingTools.constants_cache

        url = f"{prod_env}/3.1/accommodations/constants"

        payload = {"languages": ["en-us"]}

        res = await make_post_request(
            url,
            headers=BookingTools.headers,
            data=payload,
        )

        BookingTools.constants_cache = res.get("data") or {}
        BookingTools._create_reverse_accomodation_facility_lookup_by_id()
        BookingTools._create_reverse_room_facility_lookup_by_id()
        return BookingTools.constants_cache or {}

    @staticmethod
    def _create_reverse_accomodation_facility_lookup_by_id():
        try:
            if BookingTools.reverse_accomodation_facility_lookup_by_id is None:
                reverse_accomodation_facility_lookup_by_id = {}
                if BookingTools.constants_cache is not None:
                    for accomodation_facility in BookingTools.constants_cache["accommodation_facilities"]:
                        if "en-us" not in accomodation_facility["name"]:
                            continue

                        reverse_accomodation_facility_lookup_by_id[accomodation_facility["id"]] = {
                            "name": accomodation_facility["name"]["en-us"],
                            "type": accomodation_facility["facility_type"],
                        }
                BookingTools.reverse_accomodation_facility_lookup_by_id = reverse_accomodation_facility_lookup_by_id
        except Exception as e:
            logger.error(e.args[0], mask=booking_log_mask)
            pass

    @staticmethod
    def _create_reverse_room_facility_lookup_by_id():
        try:
            if BookingTools.reverse_room_facility_lookup_by_id is None:
                reverse_room_facility_lookup_by_id = {}
                if BookingTools.constants_cache is not None:
                    for room_facility in BookingTools.constants_cache["room_facilities"]:
                        if "en-us" not in room_facility["name"]:
                            continue

                        reverse_room_facility_lookup_by_id[room_facility["id"]] = {
                            "name": room_facility["name"]["en-us"],
                            "type": room_facility["facility_type"],
                        }
                BookingTools.reverse_room_facility_lookup_by_id = reverse_room_facility_lookup_by_id
        except Exception as e:
            logger.error(e.args[0], mask=booking_log_mask)
            pass

    def _get_payment_timing_filter(self, payment_timings: List[HotelBookingPaymentTiming] | None = None) -> dict | None:
        """
        Helper method to create payment timing filter based on payment preferences.

        Args:
            payment_timings: List of preferred payment timings

        Returns:
            Dict containing filter configuration or None if no filter should be applied
        """
        if not payment_timings:
            return None

        # Check payment preferences
        has_pay_at_property = HotelBookingPaymentTiming.PAY_AT_THE_PROPERTY in payment_timings
        has_pay_online = any(
            timing in payment_timings
            for timing in [
                HotelBookingPaymentTiming.PAY_ONLINE_LATER,
                HotelBookingPaymentTiming.PAY_ONLINE_NOW,
            ]
        )

        # Set payment filter based on preferences
        if has_pay_at_property and not has_pay_online:
            return {"timing": "pay_at_the_property"}
        elif has_pay_online and not has_pay_at_property:
            return {"timing": "pay_online"}

        # If both or neither are selected, no filter needed
        return None

    @atime_function
    async def do_base_search_v2(
        self,
        params: dict,
        lat_long_dict: dict,
        distance_threshold: int = distance_from_target_location_threshold,
        preferred_payment_timings: List[HotelBookingPaymentTiming] | None = None,
    ) -> tuple[list[dict[str, Any]], int]:
        """
        The strategy is to:
        • Get the hotel list by region or city from DB using geospatial search
        • Cherry pick the fields we want and need, joining in the strings from the facilities, districts and landmarks caches
        • Rescan the properties list and try to match the lat/long location_neighborhoods_districts_landmarks value to the hotels
        • Send those properties to the LLM
        • Get availablity on the selected hotels
        """

        try:
            # Timing is everything
            t = Timings("Booking.com: Base search")

            hotel_params_dict = params

            # Now get brand information
            preferred_hotel_brands = hotel_params_dict.get("brand_hotel_preferences", None)
            brand_IDs = None
            if preferred_hotel_brands is not None and len(preferred_hotel_brands) > 0:
                brand_IDs = self.lookup_brand_IDs(preferred_hotel_brands)
                logger.info(f"Brand IDs {brand_IDs}", mask=booking_log_mask)

            # Now do an accomodations search from db
            properties = await self.list_hotels_data(
                lat_long_dict=lat_long_dict,
                check_in_date=hotel_params_dict.get("check_in_date", ""),
                check_out_date=hotel_params_dict.get("check_out_date", ""),
                hotel_brand_ids=brand_IDs,
                distance_threshold=distance_threshold,
                min_price=hotel_params_dict.get("min_price"),
                max_price=hotel_params_dict.get("max_price"),
                payment_timings=preferred_payment_timings,
                is_mobile=hotel_params_dict.get("is_mobile", False),
                limit=max_hotel_results,
            )
            if len(properties) == 0:
                logger.error(
                    "NO SPECIFIC BRAND HOTELS WERE WITHIN A REASONABLE RADIUS OF SPECIFIED DESTINATION/LOCATION",
                    mask=booking_log_mask,
                )
                distance_threshold = max(distance_threshold, distance_from_target_location_threshold)
                # try again but without the constraint of hotels brands
                properties = await self.list_hotels_data(
                    check_in_date=hotel_params_dict.get("check_in_date", ""),
                    check_out_date=hotel_params_dict.get("check_out_date", ""),
                    lat_long_dict=lat_long_dict,
                    min_price=hotel_params_dict.get("min_price"),
                    max_price=hotel_params_dict.get("max_price"),
                    hotel_brand_ids=None,
                    distance_threshold=distance_threshold,
                    is_mobile=hotel_params_dict.get("is_mobile", False),
                    limit=max_hotel_results,
                )

            t.print_timing("yellow")

            return properties, distance_threshold

        except Exception as e:
            logger.error(e, mask=booking_log_mask)

        return [], 0

    @atime_function
    async def list_hotels_data(
        self,
        check_in_date: str,
        check_out_date: str,
        lat_long_dict: dict,
        distance_threshold: int,
        min_price: int | None,
        max_price: int | None,
        payment_timings: List[HotelBookingPaymentTiming] | None = None,
        hotel_brand_ids: List[int] | None = None,
        is_mobile: bool = False,
        limit: int = max_hotel_results,
    ):
        target_coordinate = (lat_long_dict["latitude"], lat_long_dict["longitude"])

        if min_price or max_price:
            # If price range is given, use booking.com API to get hotels directly.
            logger.info("Use Booking", mask=booking_log_mask)
            payload = {
                "booker": {
                    "country": "us",  # Default to US
                    "platform": "mobile" if is_mobile else "desktop",
                    "travel_purpose": "business",
                },
                "checkin": check_in_date,
                "checkout": check_out_date,
                "guests": {"number_of_adults": 1, "number_of_rooms": 1},
                "coordinates": {
                    "latitude": lat_long_dict["latitude"],
                    "longitude": lat_long_dict["longitude"],
                    "radius": distance_threshold / 1000,  # API has a max radius limit
                },
            }

            payload["accommodation_types"] = supported_accommodation_types

            # Add payment timing filter if specified - using extracted utility function
            payment_filter = self._get_payment_timing_filter(payment_timings)
            if payment_filter:
                payload["payment"] = payment_filter

            # Add hotel brand filter if specified
            if hotel_brand_ids:
                payload["brands"] = hotel_brand_ids

            payload["rows"] = limit

            if min_price or max_price:
                payload["price"] = {}
                if min_price:
                    payload["price"]["minimum"] = min_price
                if max_price:
                    payload["price"]["maximum"] = max_price
            if payload.get("price"):
                payload["currency"] = "USD"
            url = f"{prod_env}/3.1/accommodations/search"
            response = await make_post_request(
                url,
                headers=self.headers,
                data=payload,
                request_filename="booking_dot_com_search_request.json",
                response_filename="booking_dot_com_search_response.json",
            )

            if not response.get("data"):
                logger.error("No data returned from Booking.com API", mask=booking_log_mask)
                return []

            # Extract hotel IDs from the API response
            hotels_data = response.get("data") or []
            hotel_ids = [hotel.get("id") for hotel in hotels_data if hotel.get("id")]

            if not hotel_ids:
                logger.error("No hotel IDs found in API response", mask=booking_log_mask)
                return []

            query: dict[str, Any] = {"id": {"$in": hotel_ids}}

            hotel_data = await hotels_data_cache_collection.find(query).to_list(length=None)

            if len(hotel_data) < len(hotel_ids):
                logger.warning(
                    f"Only found {len(hotel_data)} out of {len(hotel_ids)} hotels in MongoDB", mask=booking_log_mask
                )

            # Post-process the hotel data to add required fields
            processed_hotels = []
            for hotel in hotel_data:
                processed_hotel = await self.process_hotel(hotel)

                hotel_coordinate = (
                    hotel["location"]["coordinates"]["latitude"],
                    hotel["location"]["coordinates"]["longitude"],
                )
                distance_to_target = self.calc_property_distance_in_meters(target_coordinate, hotel_coordinate)
                processed_hotel["distance_to_target_in_miles"] = distance_to_target / one_mile_in_meters

                processed_hotels.append(processed_hotel)

            # Sort the hotels by distance to target
            processed_hotels.sort(key=lambda x: x["distance_to_target_in_miles"])

            return processed_hotels

        else:
            logger.info("Use MongoDB", mask=booking_log_mask)

            # Build the base query with geospatial search
            query: dict[str, Any] = {
                "geo_location": {
                    "$nearSphere": {
                        "$geometry": {
                            "type": "Point",
                            "coordinates": [
                                float(target_coordinate[1]),
                                float(target_coordinate[0]),
                            ],  # [longitude, latitude]
                        },
                        "$maxDistance": distance_threshold,
                    }
                },
                "accommodation_type": {"$in": supported_accommodation_types},
            }

            # Add payment timing filter for MongoDB query
            if payment_timings:
                query["payment.timings"] = {"$in": [timing.value for timing in payment_timings]}

            # Add brand filter if specified
            if hotel_brand_ids:
                query["brands"] = {"$in": hotel_brand_ids}

            hotel_data = await hotels_data_cache_collection.find(query).limit(limit).to_list(length=None)

            # Post-process the hotel data to add required fields
            processed_hotels = []
            for hotel in hotel_data:
                processed_hotel = await self.process_hotel(hotel)

                hotel_coordinate = (
                    hotel["location"]["coordinates"]["latitude"],
                    hotel["location"]["coordinates"]["longitude"],
                )
                distance_to_target = self.calc_property_distance_in_meters(target_coordinate, hotel_coordinate)
                processed_hotel["distance_to_target_in_miles"] = distance_to_target / one_mile_in_meters

                processed_hotels.append(processed_hotel)

            # Sort the hotels by distance to target
            processed_hotels.sort(key=lambda x: x["distance_to_target_in_miles"])

            return processed_hotels

    async def send_request_via_vgs(self, create_payload: dict, url: str):
        #  JTB:  SPOTNANA_VGS_HOST not Spotnana specific
        assert settings.SPOTNANA_VGS_HOST, "SPOTNANA_VGS_HOST is not set"
        ssl_certificate_filename = "sandbox.pem" if "sandbox" in settings.SPOTNANA_VGS_HOST else "live.pem"

        res = await make_post_request(
            url,
            self.headers,
            data=create_payload,  # Looks like this should be a CreditCard obj
            proxy=f"{settings.OTTO_VGS_OUTBOUND_HOST}",
            ssl_cert_path=str(Path(__file__).parent.parent.joinpath("vgs_cert").joinpath(ssl_certificate_filename)),
        )
        return res

    def generate_fake_booking_creation_response(self):
        # Generate random values for pincode, order and reservation
        random_pincode = "".join([str(random.randint(0, 9)) for _ in range(4)])
        random_order = "".join([str(random.randint(0, 9)) for _ in range(10)])

        return {
            "request_id": "01jbaantmz5abhrfdbtazgmxny",
            "data": {
                "payment": {"receipt_url": None, "authorisation_form_url": None},
                "accommodation": {
                    "pincode": random_pincode,
                    "order": random_order,
                    "reservation": 1111111111,
                },
            },
        }

    @atime_function
    async def do_order_create(
        self,
        product_id: str | None,
        user: User,
        order_token: str,
        payment_timing: HotelBookingPaymentTiming = HotelBookingPaymentTiming.PAY_AT_THE_PROPERTY,
    ) -> dict:
        try:
            company_admin_user: UserDB | None = None
            if user.organization_id:
                company_admin_user = await UserDB.from_organization_id_and_role(
                    user.organization_id, UserRole.company_admin
                )

            (
                user_profile_personal_information,
                user_profile_payment_information,
                _,
            ) = await BookingTools.check_payment_profile(user, company_admin_user)

            assert user_profile_personal_information and user_profile_payment_information, (
                "The user has to have payment profile when doing order creation"
            )

            def transform_date(date_str):
                # Split the string by ' / '
                month, year = date_str.split(" / ")
                # Reformat the string to 'YYYY-MM'
                if len(year) == 2:
                    year = f"20{year}"
                return f"{year}-{month.zfill(2)}"

            email: str = user_profile_personal_information.get("email", None)  # type: ignore # if the key doesn't exist, returns None
            if not email:
                email = user.email  # use the user.email instead
            phone: str = user_profile_personal_information.get("phone", None)  # type: ignore
            cardholder_name: str = user_profile_payment_information.get("cardholder_name", None)  # type: ignore
            exp_date: str = transform_date(user_profile_payment_information.get("exp_date", None))  # type: ignore
            card_number: str = user_profile_payment_information.get("card_number", None)  # type: ignore
            # card_type: str = user_payment_profile.get("card_type", None)
            card_cvc: str = user_profile_payment_information.get("card_cvc", None)  # type: ignore
            first_name: str = user_profile_personal_information.get("first_name", None)  # type: ignore
            last_name: str = user_profile_personal_information.get("last_name", None)  # type: ignore
            address: str = user_profile_payment_information.get("address", None)  # type: ignore
            # state: str = user_payment_profile.get("state", None)
            city: str = user_profile_payment_information.get("city", None)  # type: ignore
            zip_code: str = user_profile_payment_information.get("zip_code", None)  # type: ignore

            payload = {
                "accommodation": {
                    "label": "Otto Booking",
                    "products": [
                        {
                            "id": product_id,
                            "guests": [
                                {
                                    "email": email,
                                    "name": f"{first_name} {last_name}",
                                }
                            ],
                        }
                    ],
                    "remarks": {
                        "estimated_arrival_time": {"hour": 12},
                        "special_requests": "None",
                    },
                },
                "booker": {
                    "address": {
                        "address_line": address,
                        "city": city,
                        "country": "us",
                        "post_code": zip_code,
                    },
                    "company": "None",
                    "email": email,
                    "language": "en-us",
                    "name": {
                        "first_name": f"{first_name}",
                        "last_name": f"{last_name}",
                    },
                    "telephone": phone,
                },
                "order_token": order_token,
                "payment": {
                    "card": {
                        "cardholder": cardholder_name,
                        "cvc": card_cvc,
                        "expiry_date": exp_date,
                        "number": card_number,
                    },
                    "include_receipt": False,  # with True you can only do this for pay_online
                    "method": "card",
                    "timing": payment_timing.value,
                },
            }

            # vgs_url can be sandbox but hotel booking didn't really work in sandbox.
            # we'll fake the booking in dev but use real booking in stg/prod.
            url = f"{prod_env}/3.1/orders/create"

            if settings.HOTEL_FAKE_BOOKING:
                logger.warning(
                    f"Hotel fake booking enabled: {settings.HOTEL_FAKE_BOOKING}, request: {payload}",
                    mask=booking_log_mask,
                )
                return self.generate_fake_booking_creation_response()
            else:
                t = Timings("Booking.com: create order")
                res = await self.send_request_via_vgs(payload, url)
                t.print_timing("yellow")

            return res

        except Exception as e:
            logger.error(e.args[0], mask=booking_log_mask)
            return {"error_response": e.args[0], "status": booking_failed_status, "data": {}}

    @atime_function
    async def do_order_preview(
        self,
        product_id: str | None,
        property_id: int | None,
        check_in_date: str | None,
        check_out_date: str | None,
        is_mobile: bool = False,
    ) -> dict | None:
        preview_payload = None

        try:
            if product_id is None or property_id is None or check_in_date is None or check_out_date is None:
                return None

            url = f"{prod_env}/3.1/orders/preview"
            payload = {
                "booker": {
                    "country": "us",
                    "platform": "mobile" if is_mobile else "desktop",
                    "travel_purpose": "business",
                },
                "currency": "USD",
                "accommodation": {
                    "id": property_id,
                    "checkin": check_in_date,
                    "checkout": check_out_date,
                    "products": [
                        {
                            "id": product_id,
                            "allocation": {
                                "number_of_adults": 1,
                            },
                        }
                    ],
                },
            }

            keep_paging = True
            while keep_paging:
                response = await make_post_request(url, data=payload, headers=self.headers)
                preview_payload = response

                next_page = response.get("next_page", None)
                if next_page is not None:
                    payload = {"page": next_page}
                else:
                    keep_paging = False

        except HTTPException as e:
            if e.status_code in client_side_parameter_error_status_codes:
                raise e
            # For other exceptions, log and return None
            logger.warn(f"Get {e.status_code} error: {e.detail}", mask=booking_log_mask)
            return None
        except Exception as e:
            logger.error(str(e), mask=booking_log_mask)
            return None

        return preview_payload

    @atime_function
    async def get_availability_and_price(
        self,
        hotel_options: List[dict[str, Any]],
        check_in_date: str,
        check_out_date: str,
        max_hotel_results=0,
        preferred_payment_timings: List[HotelBookingPaymentTiming] | None = None,
        is_mobile: bool = False,
    ) -> List[dict[str, Any]]:
        # Gather up the property IDs
        property_ids = []
        property_keyvals = []
        for hotel in hotel_options:
            property_id = hotel.get("property_id", 0)
            property_name = hotel.get("property_name", 0)
            property_keyvals.append({property_name: property_id})
            if property_id > 0:
                property_ids.append(property_id)

        logger.info(
            f"Getting details for these properties: {property_keyvals}",
            mask="\033[96m {}\033[00m",
        )

        url = f"{prod_env}/3.1/accommodations/bulk-availability"
        payload = {
            "currency": "USD",
            "booker": {
                "country": "us",
                "platform": "mobile" if is_mobile else "desktop",
                "travel_purpose": "business",
            },
            "checkin": check_in_date,
            "checkout": check_out_date,
            "extras": ["extra_charges"],
            "guests": {"number_of_adults": 1, "number_of_rooms": 1},
        }

        payment_filter = self._get_payment_timing_filter(preferred_payment_timings)
        if payment_filter:
            payload["filters"] = {"payment": payment_filter}

        payload["accommodations"] = property_ids

        # Timing is everything
        t = Timings("Booking.com: Get availability")

        keep_paging = True
        while keep_paging:
            try:
                response = await make_post_request(url, data=payload, headers=self.headers)

                if len(response["data"]) > 0:
                    for avail_obj in response["data"]:
                        hotel_option = self.lookup_hotel_option(hotel_options, avail_obj["id"])
                        if hotel_option is not None:
                            # Find the rooms in hotel_option and match them
                            # to the rooms in avail_obj
                            self.assign_products_to_rooms(avail_obj, hotel_option)
                        else:
                            logger.error(
                                "get_availability_and_price - Error: couldn't find property", mask=booking_log_mask
                            )

                next_page = response.get("next_page", None)
                if next_page is not None:
                    payload = {"page": next_page}
                else:
                    keep_paging = False
            except HTTPException as e:
                logger.error(
                    f"get_availability_and_price - HttpException: Failed to retrieve hotel options: {e.detail} (status_code: {e.status_code})",
                    mask=booking_log_mask,
                )
                raise Exception(f"Hotel API error: {e.detail} (status_code: {e.status_code})")
            except Exception as e:
                logger.error(
                    f"get_availability_and_price - Error: Failed to retrieve hotel options. {e}, traceback: {traceback.format_exc()}",
                    mask=booking_log_mask,
                )
                raise e

        t.print_timing("yellow")

        # Now prune down rooms without price for each hotel
        remove_list = []
        for index, hotel in enumerate(hotel_options):
            pruned_rooms = self.prune_rooms_without_price(hotel)
            if pruned_rooms == []:
                # remove the hotel option because there is no availability
                remove_list.append(index)
                logger.info(
                    f"No availability at {hotel['property_name'] if 'property_name' in hotel else hotel['property_id']}",
                    mask=booking_log_mask,
                )
            hotel["rooms"] = pruned_rooms
            if hotel.get("location") and hotel["location"].get("address") and hotel["location"].get("postal_code"):
                hotel["address"] = (
                    f"{hotel['location'].get('address').get('en-us')} {hotel['location'].get('postal_code')}"
                )

        available_hotels = [item for idx, item in enumerate(hotel_options) if idx not in remove_list]
        if max_hotel_results > 0:
            # reduce down return result
            if len(available_hotels) > max_hotel_results:
                logger.info(
                    f"Too many hotel options ({len(available_hotels)}) - truncating to {max_hotel_results}",
                    mask=booking_log_mask,
                )
                available_hotels = available_hotels[:max_hotel_results]

        return available_hotels

    def prune_rooms_without_price(self, hotel: dict) -> List[dict]:
        """Find the N cheapest rooms and get rid of the rest"""
        try:
            new_rooms_list = []

            if "rooms" not in hotel:
                return []

            # clean out rooms that have not product and prices
            for room in hotel["rooms"]:
                if room.get("price", None) is not None:
                    new_rooms_list.append(room)
                else:
                    # Prune it
                    pass

            return new_rooms_list

        except Exception as e:
            logger.error(f"Failed to prune rooms without price. Error: {e}", mask=booking_log_mask)

        return []

    def prune_to_cheapest_rooms(self, hotel: dict, number=3) -> List[dict]:
        """Find the N cheapest rooms and get rid of the rest"""
        try:
            new_rooms_list = []
            # clean out rooms that have not product and prices
            for room in hotel["rooms"]:
                if room.get("price", None) is not None:
                    new_rooms_list.append(room)
                else:
                    # Prune it
                    pass

            sorted_rooms = sorted(new_rooms_list, key=lambda x: x.get("price", {}).get("total", 0))
            return sorted_rooms[:number]

        except Exception as e:
            logger.error(f"Failed to prune to cheapest room. Error: {e}", mask=booking_log_mask)

        return []

    def assign_products_to_rooms(self, avail_obj: dict, hotel_option: dict):
        try:
            if "rooms" not in hotel_option:
                logger.warning(
                    f"assign_products_to_rooms() - No rooms in hotel_option: {json.dumps(hotel_option)}",
                    mask=booking_log_mask,
                )
                return
            # Go through the items in the availability object and assign them
            # to their rooms
            for product in avail_obj["products"]:
                room = self.lookup_hotel_room(hotel_option, product["room"])
                # Note that a room can have multiple products associated with
                # it - pick the cheapest one
                if room is not None:
                    current_price_obj = room.get("price", None)
                    if current_price_obj is None:
                        room["product_id"] = product["id"]
                        room["price"] = product["price"]
                        room["policies"] = product["policies"]
                    else:  # check if this price is cheaper than the one that's there, if so, replace it
                        if product["price"]["base"] < current_price_obj["base"]:
                            # Copy over it
                            room["product_id"] = product["id"]
                            room["price"] = product["price"]
                            room["policies"] = product["policies"]
                        else:
                            pass

                else:
                    logger.debug(
                        f"assign_products_to_rooms() - Couldn't find room with id: {product['id']}",
                        mask=booking_log_mask,
                    )

        except Exception as e:
            logger.error(f"Failed to assign product to room. Error: {e}", mask=booking_log_mask)
            pass

    def lookup_hotel_room(self, hotel_option: dict, product_id: str) -> dict | None:
        try:
            for room in hotel_option["rooms"]:
                if room["room_id"] == product_id:
                    return room
        except Exception as e:
            logger.error(e.args[0], mask=booking_log_mask)

        return None

    def lookup_hotel_option(self, options: List[dict], property_id: str) -> dict | None:
        """
        Its pretty simple - walk through the list and see if we can find a
        property with same id as id
        """
        for option in options:
            if option["property_id"] == property_id:
                return option
        return None

    def lookup_district(self, city: str):
        """
        see if there is a district that matches the city name (e.g. Chicago
        Metropolitan Area)
        """
        if BookingTools.districts_cache is not None:
            # There are about ~750 districts
            pass

    def lookup_city(self, city: str, lat_long: dict) -> dict:
        """
        There can be multiple cities of the same name (e.g., "Springfield")
        Disambiguate duplicate cities using Lat/long coordinates
        """
        if BookingTools.cities_cache:
            cities = BookingTools.cities_cache["cities"]

            if "," in city:
                # Hack, knock off the state - The '1' means split only at the
                # first comma
                city = city.split(",", 1)[0]

            # This is an array to account for duplicate city names
            city_info = cities.get(city, None)
            if city_info is not None:
                if len(city_info) > 1:
                    # There are multiple cities - see if we can narrow it down
                    # by lat/long
                    for c in city_info:
                        coord1 = (
                            float(lat_long["latitude"]),
                            float(lat_long["longitude"]),
                        )
                        coord2 = (
                            float(c["coordinates"]["latitude"]),
                            float(c["coordinates"]["longitude"]),
                        )
                        diff = self.calc_property_distance_in_meters(coord1, coord2)
                        if diff < one_mile_in_meters:
                            # pick the first one
                            return c
                else:
                    return city_info[0]
        return {}

    def lookup_districts_by_id(self, district_id: str) -> dict:
        if BookingTools.districts_cache is not None:
            districts = BookingTools.districts_cache["districts"]
            # This may fail
            district_info = districts.get(district_id, None)
            if district_info is not None:
                return district_info
        return {}

    def lookup_districts_by_name(self, district_name: str) -> dict:
        if BookingTools.reverse_districts_lookup_by_name is not None:
            districts = BookingTools.reverse_districts_lookup_by_name["districts"]
            # returns an array
            district_candidates = districts.get(district_name, None)
            if district_candidates is not None:
                return district_candidates[0]
        return {}

    # This could be districts
    def lookup_landmarks(self, landmark: str) -> dict:
        if BookingTools.landmarks_cache is not None:
            landmarks = BookingTools.landmarks_cache["landmarks"]
            # This may fail
            landmark_info = landmarks.get(landmark, None)
            if landmark_info is not None:
                # We've got a problem
                return landmark_info
        return {}

    def lookup_brand_IDs(self, brands: List[str]) -> List[int]:
        brand_ids = []
        if BookingTools.chains_cache is not None:
            # Linear search :(
            for brand in brands:
                # I don't love this - we really need some kind of more
                # efficient fuzzy lookup mechanism that is better than O(n)
                # time
                filtered_data = [
                    value for key, value in BookingTools.chains_cache.items() if brand.lower() in key.lower()
                ]
                brand_ids.extend(filtered_data)
        return brand_ids

    async def process_hotel(self, hotel: dict) -> dict:
        # Cherrypick the information we want/need

        try:
            assert BookingTools.reverse_cities_lookup_by_id is not None, (
                "reverse_cities_lookup_by_id failed to be initialized"
            )
            filtered_dict = {}
            filtered_dict["property_id"] = hotel["id"]
            filtered_dict["property_name"] = hotel["name"]["en-us"]
            filtered_dict["description_text"] = hotel.get("description", {}).get("text", {}).get("en-us", "")
            filtered_dict["location"] = {
                "address": hotel["location"]["address"],
                "city": BookingTools.reverse_cities_lookup_by_id.get(hotel["location"]["city"], ""),
                "coordinates": hotel["location"]["coordinates"],
                "postal_code": hotel["location"]["postal_code"],
            }

            main_accomodation_photo = None
            photos = []
            for photo in hotel["photos"]:
                photos.append(photo["url"]["standard"])
                # Find the main photo
                if photo.get("main_accomodation_photo", None) is not None:
                    main_accomodation_photo = photo["url"]["standard"]
                    filtered_dict["photo_main_url"] = main_accomodation_photo

                if photo.get("main_photo", None) is not None:
                    main_accomodation_photo = photo["url"]["standard"]
                    filtered_dict["photo_main_url"] = main_accomodation_photo

            # we didn't fond a 'main' photo
            if main_accomodation_photo is None:
                main_accomodation_photo = hotel["photos"][0]["url"]["standard"]
                filtered_dict["photo_main_url"] = main_accomodation_photo

            filtered_dict["photos"] = photos
            filtered_dict["rating"] = hotel["rating"]
            filtered_dict["dropoff_url"] = hotel["url"]
            filtered_dict["checkin_checkout_times"] = hotel["checkin_checkout_times"]

            # Go through the facilities for the accomodation and look up their
            # strings (throw them all into one bucket)
            hotel_facilities_set = set()
            for facility_id in hotel["facilities"]:
                fid = facility_id["id"]
                if fid in accomodation_positive_facility_ids:
                    facility_name = await BookingTools.get_accomodation_facility_name_by_id(fid)
                    if facility_name is not None:
                        hotel_facilities_set.add(facility_name)

            # Go through the rooms to accumulate their room_facilities info and
            # other important elements (e.g., photos)
            rooms_list = []
            # Hypothesis that not all of the rooms have a photo, but some (or
            # one) might
            cached_room_photo = None
            one_or_more_photos_exist = False
            one_or_more_missing_photos = False
            has_a_main_room_photo = False

            for _, room in enumerate(hotel["rooms"]):
                description = room.get("description", {}).get("en-us", info_not_available)
                name = room.get("name", {}).get("en-us", info_not_available)
                truncated_room = {
                    "room_id": room["id"],
                    "title": name,
                    "description": description,
                }
                # Get the room photo

                photos = room.get("photos", [])  # This is an array of objs
                # 'photos' can be none
                if photos is not None and len(photos) > 0:
                    one_or_more_photos_exist = True
                    photo_url = None
                    for photo in photos:
                        if photo.get("main_photo", None):
                            has_a_main_room_photo = True
                            photo_url = photo["url"]["standard"]
                            break
                    if photo_url is None:
                        photo_url = random.choice(photos).get("url", {}).get("standard", "")

                    truncated_room["room_photo"] = photo_url
                    cached_room_photo = photo_url
                else:
                    one_or_more_missing_photos = True
                    if cached_room_photo is not None:
                        # use one of the other room photos
                        truncated_room["room_photo"] = cached_room_photo
                    else:
                        truncated_room["room_photo"] = main_accomodation_photo  # use the main hotel photo

                # get the facilities info for room
                room_facility_ids = room["facilities"]
                room_facilities_set = set()
                for room_facility_id in room_facility_ids:
                    room_facility_name = await BookingTools.get_room_facility_name_by_id(room_facility_id)
                    if room_facility_name is not None:
                        room_facilities_set.add(room_facility_name)

                truncated_room["amenities"] = list(room_facilities_set)
                rooms_list.append(truncated_room)

            logger.debug(
                f"Room image report for property: {filtered_dict['property_name']}:  Has a room 'main_photo': {has_a_main_room_photo}, Has one or more room photos: {one_or_more_photos_exist}, Has missing room photos: {one_or_more_missing_photos}",
                mask=booking_log_mask,
            )

            # at the hotel level Facilities
            filtered_dict["facilities"] = list(hotel_facilities_set)

            filtered_dict["rooms"] = list(rooms_list)  # Tak this on, we'll remove it (manage it) later

            # Now join districts and landmarks - be preared for them to be
            # absent or empty array
            districts_list = []
            location_info = hotel["location"]
            districts = location_info.get("districts", None)
            if districts is not None and districts != []:
                for district in districts:
                    district = self.lookup_districts_by_id(str(district))
                    if district != {}:
                        districts_list.append(district["name"])

            if districts_list != []:
                filtered_dict["districts"] = districts_list

            # JTB:  Todo add landmarks (although this will blow up the payload event more)
            # location['landmarks'] =
            # logger.info(json.dumps(filtered_dict) + "\n\n")

            return filtered_dict

        except Exception as e:
            logger.error(e.args[0], mask=booking_log_mask)
            pass

        return {}

    async def process_hotel_from_api(self, hotel: dict) -> dict:
        """
        Process a hotel from the Booking.com API response.
        Similar to process_hotel but adapted for the API response structure.

        Args:
            hotel: Hotel data from the Booking.com API

        Returns:
            Processed hotel data in the same format as process_hotel
        """
        try:
            filtered_dict = {}
            filtered_dict["property_id"] = hotel.get("id", "")
            filtered_dict["property_name"] = hotel.get("name", "")
            filtered_dict["description_text"] = hotel.get("description", "")

            # Process location
            location = hotel.get("location", {})
            filtered_dict["location"] = {
                "address": location.get("address", {}),
                "city": location.get("city", {}).get("name", ""),
                "coordinates": {
                    "latitude": location.get("coordinates", {}).get("latitude", 0),
                    "longitude": location.get("coordinates", {}).get("longitude", 0),
                },
                "postal_code": location.get("postal_code", ""),
            }

            # Process photos
            main_accommodation_photo = None
            photos = []
            for photo in hotel.get("photos", []):
                photo_url = photo.get("url", {}).get("standard", "")
                if photo_url:
                    photos.append(photo_url)
                    # Find the main photo
                    if photo.get("main_accommodation_photo", False) or photo.get("main_photo", False):
                        main_accommodation_photo = photo_url
                        filtered_dict["photo_main_url"] = main_accommodation_photo

            # If no main photo was found, use the first one
            if not main_accommodation_photo and photos:
                main_accommodation_photo = photos[0]
                filtered_dict["photo_main_url"] = main_accommodation_photo

            filtered_dict["photos"] = photos
            filtered_dict["rating"] = hotel.get("rating", 0)
            filtered_dict["dropoff_url"] = hotel.get("url", "")
            filtered_dict["checkin_checkout_times"] = hotel.get("checkin_checkout_times", {})

            # Process facilities
            hotel_facilities_set = set()
            for facility in hotel.get("facilities", []):
                facility_id = facility.get("id")
                if facility_id in accomodation_positive_facility_ids:
                    facility_name = await BookingTools.get_accomodation_facility_name_by_id(facility_id)
                    if facility_name is not None:
                        hotel_facilities_set.add(facility_name)

            filtered_dict["facilities"] = list(hotel_facilities_set)

            # Process rooms
            rooms_list = []
            cached_room_photo = None
            one_or_more_photos_exist = False
            one_or_more_missing_photos = False
            has_a_main_room_photo = False

            for room in hotel.get("rooms", []):
                truncated_room = {
                    "room_id": room.get("id", ""),
                    "title": room.get("name", info_not_available),
                    "description": room.get("description", info_not_available),
                }

                # Process room photos
                room_photos = room.get("photos", [])
                if room_photos:
                    one_or_more_photos_exist = True
                    photo_url = None
                    for photo in room_photos:
                        if photo.get("main_photo", False):
                            has_a_main_room_photo = True
                            photo_url = photo.get("url", {}).get("standard", "")
                            break

                    if not photo_url and room_photos:
                        photo_url = random.choice(room_photos).get("url", {}).get("standard", "")

                    if photo_url:
                        truncated_room["room_photo"] = photo_url
                        cached_room_photo = photo_url
                else:
                    one_or_more_missing_photos = True
                    if cached_room_photo:
                        truncated_room["room_photo"] = cached_room_photo
                    elif main_accommodation_photo:
                        truncated_room["room_photo"] = main_accommodation_photo

                # Process room facilities
                room_facilities_set = set()
                for facility_id in room.get("facilities", []):
                    room_facility_name = await BookingTools.get_room_facility_name_by_id(facility_id)
                    if room_facility_name is not None:
                        room_facilities_set.add(room_facility_name)

                truncated_room["amenities"] = list(room_facilities_set)
                rooms_list.append(truncated_room)

            if filtered_dict.get("property_name"):
                logger.debug(
                    f"Room image report for property: {filtered_dict['property_name']}:  Has a room 'main_photo': {has_a_main_room_photo}, Has one or more room photos: {one_or_more_photos_exist}, Has missing room photos: {one_or_more_missing_photos}",
                    mask=booking_log_mask,
                )

            filtered_dict["rooms"] = rooms_list

            # Process districts
            districts_list = []
            districts = location.get("districts", [])
            if districts:
                for district_id in districts:
                    district = self.lookup_districts_by_id(str(district_id))
                    if district:
                        districts_list.append(district.get("name", ""))

            if districts_list:
                filtered_dict["districts"] = districts_list

            return filtered_dict

        except Exception as e:
            logger.error(f"Error processing hotel from API: {str(e)}", mask=booking_log_mask)
            return {}

    # Fuzzy match function - We might need this someday
    # from fuzzywuzzy import process
    # def fuzzy_match_dict(query, dictionary, limit=1):
    #     keys = list(dictionary.keys())
    #     result = process.extract(query, keys, limit=limit)
    #     return {key: dictionary[key] for key, score in result}

    # Create more efficient representations of the support data so we look up
    # IDs quickly and efficiently
    def create_reverse_cities_lookup_by_id(self):
        try:
            if BookingTools.reverse_cities_lookup_by_id is None:
                reverse_cities_lookup_by_id = {}
                if BookingTools.cities_cache is not None:
                    cities_dict = BookingTools.cities_cache["cities"]
                    for city_key in cities_dict:
                        # This is an array so make sure you get all of the entries
                        # (e.g., all of the 'Springfields')
                        for city in cities_dict[city_key]:
                            reverse_cities_lookup_by_id[city["id"]] = city_key
                            # Note, not storing lat/long
                BookingTools.reverse_cities_lookup_by_id = reverse_cities_lookup_by_id
        except Exception as e:
            logger.error(e.args[0], mask=booking_log_mask)
            pass

    # Create a more efficient representation of the faciliaties data so we
    # look up IDs quickly and efficiently
    def create_reverse_districts_lookup_by_name(self):
        try:
            if BookingTools.reverse_districts_lookup_by_name is None:
                reverse_districts_lookup_by_name = {}
                if BookingTools.districts_cache is not None:
                    for district_id in BookingTools.districts_cache["districts"]:
                        district_info = BookingTools.districts_cache["districts"][district_id]
                        # There are name duplicates in the data (e.g., "Capitol
                        # Hill"), so make sure not to clobber them
                        if reverse_districts_lookup_by_name.get(district_info["name"], None) is None:
                            reverse_districts_lookup_by_name[district_info["name"]] = [
                                {
                                    "id": district_id,
                                    "coordinates": district_info["coordinates"],
                                }
                            ]
                        else:
                            reverse_districts_lookup_by_name[district_info["name"]].append(
                                {
                                    "id": district_id,
                                    "coordinates": district_info["coordinates"],
                                }
                            )
                BookingTools.reverse_districts_lookup_by_name = reverse_districts_lookup_by_name
        except Exception as e:
            logger.error(e.args[0], mask=booking_log_mask)
            pass

    def load_caches(self):
        if (
            BookingTools.cities_cache is not None
            and BookingTools.constants_cache is not None
            and BookingTools.chains_cache is not None
            and BookingTools.districts_cache is not None
            and BookingTools.regions_cache is not None
        ):
            logger.debug("load_caches() - caches already loaded", mask=booking_log_mask)
        else:
            t = Timings("Booking.com: Load caches")
            try:
                if BookingTools.cities_cache is None:
                    with open(cities_cache_file_name, "r") as file:
                        contents = file.read()
                        BookingTools.cities_cache = json.loads(contents)

                if BookingTools.chains_cache is None:
                    with open(chains_cache_file_name, "r") as file:
                        contents = file.read()
                        BookingTools.chains_cache = json.loads(contents)

                if BookingTools.districts_cache is None:
                    with open(districts_cache_file_name, "r") as file:
                        contents = file.read()
                        BookingTools.districts_cache = json.loads(contents)

                if BookingTools.regions_cache is None:
                    with open(regions_cache_file_name, "r") as file:
                        contents = file.read()
                        BookingTools.regions_cache = json.loads(contents)

                # There is too much data to load this into memory - probably better to make direct calls to the API
                # if BookingTools.landmarks_cache == None:
                #     with open(landmarks_cache_file_name, 'r') as file:
                #         contents = file.read()
                #         BookingTools.landmarks_cache = json.loads(contents)

                # Build some fast lookup dicts
                self.create_reverse_districts_lookup_by_name()
                self.create_reverse_cities_lookup_by_id()
                t.print_timing("yellow")
            except Exception as e:
                logger.error(f"Exception while loading booking cache, error: {e}", mask=booking_log_mask)

    def calc_property_distance_in_meters(self, coord1, coord2) -> float:
        """
        Determines if two geographic coordinates are meaningfully different based on a distance threshold.

        Args:
        coord1 (tuple): The latitude and longitude of the first location (lat, long).
        coord2 (tuple): The latitude and longitude of the second location (lat, long).
        threshold (float): The threshold in meters to consider coordinates meaningfully different.

        Returns:
        bool: True if the difference is meaningful, False otherwise.
        """
        # Radius of Earth in meters
        R = 6373000

        # Convert latitude and longitude from degrees to radians
        lat1, lon1 = map(radians, coord1)
        lat2, lon2 = map(radians, coord2)

        # Compute differences
        dlat = lat2 - lat1
        dlon = lon2 - lon1

        # Haversine formula
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * atan2(sqrt(a), sqrt(1 - a))
        distance = R * c

        return distance

    def generate_fake_booking_cancel_response(self):
        return {"request_id": "01fr9ez700exycb98w90w5r9sh", "data": {"status": "successful"}}

    @atime_function
    async def cancel_order(self, order_id: str, reason: str = "Cancelled by user") -> CancellationResponse:
        """
        Cancel a booking order.

        Args:
            order_id (str): The unique identifier for the order to cancel
            reason (str, optional): Reason for cancellation. Defaults to "Cancelled by user"

        Returns:
            dict: Response containing cancellation status or error details
        """
        try:
            url = f"{prod_env}/3.1/orders/cancel"

            payload = {"order": order_id, "reason": reason}

            response = None
            if settings.HOTEL_FAKE_BOOKING:
                response = self.generate_fake_booking_cancel_response()
            else:
                t = Timings("Booking.com: cancel order")
                response = await self.send_request_via_vgs(payload, url)
                t.print_timing("yellow")

            # Check if cancellation was successful
            if response.get("data", {}).get("status") == "successful":
                logger.info(f"Successfully cancelled order {order_id}", mask=booking_log_mask)
                return CancellationResponse(status=ApiResponseStatus.SUCCESS, message="Order cancelled successfully")
            else:
                logger.error(f"Failed to cancel order {order_id}: {response}", mask=booking_log_mask)
                return CancellationResponse(
                    status=ApiResponseStatus.ERROR, message="Cancellation failed", details=response
                )

        except HTTPException as e:
            error_message = f"HTTP error while cancelling order {order_id}: {e.detail} (status: {e.status_code})"
            logger.error(error_message, mask=booking_log_mask)
            return CancellationResponse(status=ApiResponseStatus.ERROR, message=error_message, error_code=e.status_code)

        except Exception as e:
            error_message = f"Error cancelling order {order_id}: {str(e)}"
            logger.error(error_message, mask=booking_log_mask)
            return CancellationResponse(status=ApiResponseStatus.ERROR, message=error_message)

    def generate_fake_order_details_response(self):
        try:
            logger.warning("Generating fake order details response", mask=booking_log_mask)
            with open("data/fake_hotel_booking.json", "r") as file:
                return json.load(file)
        except Exception as e:
            logger.error(f"Error generating fake order details response: {str(e)}", mask=booking_log_mask)
            return {}

    @atime_function
    async def get_order_details(self, order_id: str) -> BookingDetails:
        """
        Get details for a specific order

        Args:
            order_id (str): The unique identifier for the order

        Returns:
            BookingDetails: Structured order details including status and cancellation policies
        """
        try:
            url = f"{prod_env}/3.1/orders/details/accommodations"

            payload = {
                "orders": [order_id],
                "currency": "USD",
                "extras": ["policies", "extra_charges", "accommodation_details"],
            }

            response = {}
            if settings.HOTEL_FAKE_BOOKING:
                response = self.generate_fake_order_details_response()
            else:
                t = Timings("Booking.com: order preview")
                response = await self.send_request_via_vgs(payload, url)
                t.print_timing("yellow")

            if not response.get("data"):
                error_message = f"No data found for order {order_id}"
                logger.error(error_message, mask=booking_log_mask)
                return BookingDetails.from_api_response({"error_message": error_message})

            # Create BookingDetails object from response
            order_details = BookingDetails.from_api_response(response)

            # Log any errors that occurred during parsing
            if order_details.error_message:
                logger.error(f"Error parsing order details: {order_details.error_message}", mask=booking_log_mask)

            return order_details

        except HTTPException as e:
            error_message = (
                f"HTTP error while getting order details for {order_id}: {e.detail} (status: {e.status_code})"
            )
            logger.error(error_message, mask=booking_log_mask)
            return BookingDetails.from_api_response({"error_message": error_message})

        except Exception as e:
            error_message = f"Error getting order details for {order_id}: {str(e)}"
            logger.error(error_message, mask=booking_log_mask)
            return BookingDetails.from_api_response({"error_message": error_message})

    @staticmethod
    async def check_payment_profile(user: User, admin_user: User | UserDB | None = None):
        display_payment_profile: bool = False

        get_user_profile_tasks = [
            get_user_profile_personal_information(user.id),
            get_user_profile_payment_information(user.id),
        ]
        if admin_user:
            get_user_profile_tasks.append(get_user_profile_payment_information(admin_user.id))

        (user_profile_personal_information, user_profile_payment_information, *rest) = await asyncio.gather(
            *get_user_profile_tasks
        )
        admin_payment_information = rest[0] if admin_user else None
        is_user_profile_complete = is_hotel_user_profile_personal_information_complete(
            user_profile_personal_information
        ) and (
            is_user_profile_payment_information_complete(user_profile_payment_information)
            or is_user_profile_payment_information_complete(admin_payment_information)
        )

        if not is_user_profile_complete:
            display_payment_profile = True

        return (
            user_profile_personal_information,
            admin_payment_information or user_profile_payment_information,
            display_payment_profile,
        )


if __name__ == "__main__":
    bt = BookingTools()
    # bt.collect_constants()
    # bt.collect_regions()
    # bt.collect_landmarks()
    # bt.load_caches()
    # bt.collect_landmarks()
    # bt.load_caches()
    #
    #  if BookingTools.details_cache == None:
    #     with open(accomodations_details_cache_file_name, 'r') as file:
    #         contents = file.read()
    #         BookingTools.details_cache = json.loads(contents)

    #     for index, accomodation in enumerate(BookingTools.details_cache['data']):
    #         print(f"{index}, {accomodation['name']}")

    # hotel_params = {
    #     "city":"Los Angeles",
    #     "checkin": "2024-10-15",
    #     "checkout": "2024-10-18",
    #     "preferred_hotel_brands": ["hilton", "marriott", "ramada"]
    # }
    # bt.do_base_search(json.dumps(hotel_params))

    # bt.collect_constants()
    # bt.collect_chains()
