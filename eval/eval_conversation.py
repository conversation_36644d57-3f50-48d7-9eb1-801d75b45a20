import asyncio
import j<PERSON>
from datetime import datetime, timedelta
from typing import Any, Dict, List

from langfuse import <PERSON><PERSON>
from langfuse.model import DatasetStatus

# from langchain_core.messages import BaseMessage, HumanMessage, messages_from_dict
# from langfuse import Langfuse
from sqlalchemy import select

from baml_client import b
from eval.constants import LF
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.utils.pg_connector import async_session
from server.utils.settings import settings

# import env variables
print(settings.OTTO_ENV)

# Initialize Langfuse client
langfuse = Langfuse()


async def create_conversation_dataset(days_back: int = 14) -> List[Dict[str, Any]]:
    """
    Create a dataset of conversations from the last 14 days.

    :param days_back: Number of days to go back from now to fetch conversations.
    :return: List of dictionaries containing conversation history and messages.
    """
    # Calculate the date threshold (14 days ago from now)
    date_threshold = datetime.now() - timedelta(days=days_back)

    async with async_session() as session:
        async with session.begin():
            # First get unique thread IDs from the last 14 days with a limit
            # Join with Cha<PERSON><PERSON>hread to filter by created_date
            thread_query = select(ChatThread.id).where(ChatThread.created_date >= date_threshold)
            thread_result = await session.execute(thread_query)
            thread_ids = [row[0] for row in thread_result.fetchall()]

            # Fetch checkpoints for these threads only
            query = (
                select(Checkpoint)
                .where(Checkpoint.thread_id.in_(thread_ids))
                .order_by(Checkpoint.thread_id.asc(), Checkpoint.created_date.asc())
            )
            result = await session.execute(query)
            checkpoints = result.fetchall()

    print(f"total threads: {len(thread_ids)=}")

    # Group checkpoints by thread_id
    thread_checkpoints = {}
    for row in checkpoints:
        checkpoint = row[0]
        thread_id = checkpoint.thread_id

        if thread_id not in thread_checkpoints:
            thread_checkpoints[thread_id] = []

        thread_checkpoints[thread_id].append(checkpoint)

    # Create dataset rows
    dataset_rows = []
    for thread_id, checkpoints_list in thread_checkpoints.items():
        for i, checkpoint in enumerate(checkpoints_list):
            # Extract current message from checkpoint.data

            current_data = None
            if isinstance(checkpoint.data, str):
                current_data = json.loads(checkpoint.data).get("data")
            elif isinstance(checkpoint.data, dict):
                current_data = checkpoint.data.get("data")

            if (
                not current_data
                or current_data["type"] == "human"
                or current_data["additional_kwargs"].get("timestamp")
            ):
                continue

            current_message = convert_checkpoint_data_into_string(checkpoint.data)
            # Build history from all previous checkpoints in the same thread
            history = []
            for checkpoint in checkpoints_list[:i]:
                message = convert_checkpoint_data_into_string(checkpoint.data)
                if message:
                    history.append(message)
            if not history:
                continue
            # Only add row if current message exists
            if current_message:
                dataset_rows.append(
                    {
                        "history": history,
                        "message": current_message,
                        "env": settings.OTTO_ENV.lower(),
                        "thread_id": thread_id,
                        "checkpoint_id": checkpoint.id,
                    }
                )

    return dataset_rows


# override the llm_util function to reduce the size of conversation history
def convert_checkpoint_data_into_string(checkpoint_data: dict | str) -> str | None:
    data = json.loads(checkpoint_data).get("data") if isinstance(checkpoint_data, str) else checkpoint_data.get("data")

    if not data:
        return None

    message_type = data.get("type")
    message = data.get("content")
    if not message:
        response_string = data.get("additional_kwargs", {}).get("function_call", {}).get("arguments", "{}")
        message = json.loads(response_string)
        message = message.get("agent_response", "") or message.get("presentation_message", "")

    if message:
        role = "user" if message_type == "human" else "assistant"
        return f"{role}: {message}"
    return None


async def upload_dataset(days_back: int = 14):
    """Create a dataset for evaluating conversation results."""

    dataset = await create_conversation_dataset(days_back)
    print(f"Dataset created with {len(dataset)} entries.")

    if dataset:
        dataset_name = LF.DataSet.OTTO_CONVERSATION.value
        try:
            langfuse.get_dataset(dataset_name)
        except Exception:
            langfuse.create_dataset(name=dataset_name)
            print("create new dataset:", dataset_name)

        for i, row in enumerate(dataset):
            print(f"uplodaing: {i}/{len(dataset)}")
            langfuse.create_dataset_item(
                dataset_name=dataset_name,
                input={"history": row["history"], "message": row["message"]},
                metadata={"thread_id": row["thread_id"], "checkpoint_id": row["checkpoint_id"], "env": row["env"]},
            )


async def eval_conversation():
    # experiment_name = f"{LF.Experiment.CONVERSATION_QUALITY_EXPERIMENT.value}-{datetime.now().strftime('%m%d%H%M')}"
    experiment_name = f"{datetime.now().strftime('%m/%d %H%:%M')}"
    dataset_name = LF.DataSet.OTTO_CONVERSATION.value
    dataset = langfuse.get_dataset(dataset_name)

    print(f"Running experiment: {experiment_name} on dataset: {dataset.name}")
    active_count = 0
    for i, item in enumerate(dataset.items[:100]):
        print(f"Running: {i} / {len(dataset.items)}", item.id, item.status)
        if item.status == DatasetStatus.ACTIVE:
            active_count += 1
            r = await b.EvaluateOttoResponse(item.input["history"], item.input["message"])

            total_score = r.grammar + r.consistency + r.conciseness + r.use_of_tone
            if total_score < 3:
                with item.run(
                    run_name=experiment_name + " bad only", run_metadata={"total count": active_count}
                ) as span:
                    span.score_trace(name=LF.Score.GRAMMAR_SCORE.value, value=r.grammar)
                    span.score_trace(name=LF.Score.CONSISTENCY_SCORE.value, value=r.consistency)
                    span.score_trace(name=LF.Score.CONCISENESS_SCORE.value, value=r.conciseness)
                    span.score_trace(name=LF.Score.USE_OF_TONE_SCORE.value, value=r.use_of_tone)
                    span.update_trace(input=item.input, output=r.suggested_changes)

            with item.run(run_name=experiment_name + " full", run_metadata={"total count": active_count}) as span:
                span.score_trace(name=LF.Score.GRAMMAR_SCORE.value, value=r.grammar)
                span.score_trace(name=LF.Score.CONSISTENCY_SCORE.value, value=r.consistency)
                span.score_trace(name=LF.Score.CONCISENESS_SCORE.value, value=r.conciseness)
                span.score_trace(name=LF.Score.USE_OF_TONE_SCORE.value, value=r.use_of_tone)
            # archive data item after evaluation.
            langfuse.create_dataset_item(dataset_name=dataset_name, id=item.id, status=DatasetStatus.ARCHIVED)


async def archive_items():
    dataset_name = LF.DataSet.OTTO_CONVERSATION.value
    dataset = langfuse.get_dataset(dataset_name)
    for i, item in enumerate(dataset.items[:]):
        print(item.id, item.status, item.metadata)
        # langfuse.create_dataset_item(dataset_name=dataset_name, id=item.id, status=DatasetStatus.ACTIVE)
        if item.status == DatasetStatus.ACTIVE:
            langfuse.create_dataset_item(dataset_name=dataset_name, id=item.id, status=DatasetStatus.ARCHIVED)
        await asyncio.sleep(1)  # rate limit: Datasets: 100 requests per minute


async def main():
    await upload_dataset(days_back=1)

    await eval_conversation()


if __name__ == "__main__":
    if langfuse.auth_check():
        print("Langfuse client is authenticated and ready!")
    else:
        print("Authentication failed. Please check your credentials and host.")

    # asyncio.run(archive_items())
    asyncio.run(main())
