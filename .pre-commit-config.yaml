default_language_version:
  python: python3

exclude: |
  (?x)(
      ^baml_src/
      ^data/
  )
repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.5
    hooks:
      # Run the linter.
      - id: ruff
        args: [--fix]
      # Run the formatter.
      - id: ruff-format
  - repo: https://github.com/RobertCraigie/pyright-python
    rev: v1.1.394
    hooks:
      - id: pyright
        args: ["."]
