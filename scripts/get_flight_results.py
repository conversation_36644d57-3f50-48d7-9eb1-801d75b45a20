import argparse
import async<PERSON>
import json
import os
import time
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from baml_client.types import FlightType
from flight_agent.flights_helper import FlightBamlHelper
from flight_agent.flights_tools import FlightSearchTools
from front_of_house_agent.common_models import FlightSearchParams
from front_of_house_agent.serp_flight_helper import SerpFlightSearchHelper
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.services.task_manager.task_manager import GlobalTaskManager
from server.utils.s3_utils import S3Utils

TEST_USER = User(
    id=1,
    email="test",
    profile_picture="test.jpg",
    first_name="test",
    last_name="test",
    preferred_name="test",
    created_date=datetime.fromisoformat("2023-01-01"),
    tutorial_completed=True,
    citizenship=["USA"],
)

ROOT_FOLDER = "data"
BUCKET_NAME = "otto-flights"


def generate_flight_search_params_from_query(
    query: Dict[str, Any], include_additional_criteria: bool = False
) -> Optional[FlightSearchParams]:
    """
    Generate FlightSearchParams from a query in the test_queries_v9 format.

    Args:
        query: Dictionary containing flight query data

    Returns:
        FlightSearchParams object
    """
    core = query.get("flight_search_core_criteria", {})
    additional = query.get("flight_search_additional_criteria", {})
    saved_prefs = query.get("saved_preferences", {})

    flight_type = FlightType.RoundTrip
    if core.get("flight_type") == "OneWay":
        flight_type = FlightType.OneWay

    # Get preferred airline codes from additional criteria or saved preferences
    preferred_airline_codes = additional.get("preferred_airline_codes", [])
    default_airline_brands = saved_prefs.get("preferred_airline_brands", [])

    # Get cabin preference from additional criteria or saved preferences
    preferred_cabin = additional.get("preferred_cabin") or saved_prefs.get("preferred_cabin")

    # Create travel context from additional criteria
    travel_context = json.dumps(additional) if additional else None

    # Check and adjust dates if they're in the past
    outbound_date = core.get("outbound_date")
    return_date = core.get("return_date")
    if outbound_date:
        outbound_datetime = datetime.strptime(outbound_date, "%Y-%m-%d").date()
        if outbound_datetime < datetime.strptime("2025-06-16", "%Y-%m-%d").date():
            print(f"Outbound date {outbound_date} is in the past, dropping query")
            return None

    flight_search_dict: FlightSearchParams = {
        "current_step": "OUTBOUND_FLIGHT_SEARCH",
        "search_purpose": "DATA_COLLECTION",
        "departure_airport_code": core.get("departure_airport_code"),
        "is_departure_iata_city_code": core.get("is_departure_iata_city_code", False),
        "arrival_airport_code": core.get("arrival_airport_code"),
        "is_arrival_iata_city_code": core.get("is_arrival_iata_city_code", False),
        "outbound_date": outbound_date,
        "return_date": return_date,
        "flight_type": flight_type,
        "search_id": None,
        "preferred_airline_codes": preferred_airline_codes if include_additional_criteria else None,
        "default_airline_brands": default_airline_brands if include_additional_criteria else None,
        "preferred_cabin": preferred_cabin if include_additional_criteria else None,
        "travel_context": travel_context if include_additional_criteria else None,
        "outbound_departure_time": additional.get("outbound_departure_time") if include_additional_criteria else None,
        "outbound_arrival_time": additional.get("outbound_arrival_time") if include_additional_criteria else None,
        "return_departure_time": additional.get("return_departure_time") if include_additional_criteria else None,
        "return_arrival_time": additional.get("return_arrival_time") if include_additional_criteria else None,
        "number_of_stops": 1,  # We want always to get one stop or less flights if possible
        "selected_outbound_flight_id": None,
        "search_segments": None,
    }
    return flight_search_dict


def read_flight_data_from_file(file_name: str) -> list[Dict[str, Any]]:
    """
    Read flight data from a JSON file in the scripts/data folder.

    Args:
        file_name: Name of the file to read (with or without .json extension)

    Returns:
        Dict containing the parsed JSON data
    """
    if not file_name.endswith(".json"):
        file_name += ".json"

    file_path = os.path.join("scripts", "data", file_name)

    try:
        with open(file_path, "r") as file:
            data = json.load(file)
        return data
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return []
    except json.JSONDecodeError:
        print(f"Error: File {file_path} contains invalid JSON")
        return []


def dummy_schedule_send_message_fn(message: dict[str, Any]) -> None:
    pass


async def process_flight_queries(
    input_file: str, include_additional_criteria: bool = False, start_index: int = 0, end_index: Optional[int] = None
):
    queries = read_flight_data_from_file(input_file)
    if not queries:
        print("No queries found to process")
        return

    print(f"Found {len(queries)} flight queries to process")
    s3 = S3Utils()
    queries_run_in_window = 0
    queries_to_save = []
    window_start_time = time.time()
    WINDOW_DURATION = 30 * 60  # 30 minutes in seconds
    MAX_QUERIES_PER_WINDOW = 10  # 10 queries per 30 minutes

    try:
        for i, query in enumerate(queries[start_index:end_index], start_index + 1):
            # --- Rate limit check ---
            if queries_run_in_window >= MAX_QUERIES_PER_WINDOW:
                elapsed = time.time() - window_start_time
                if elapsed < WINDOW_DURATION:
                    sleep_time = WINDOW_DURATION - elapsed
                    print(f"Rate limit reached. Sleeping for {sleep_time / 60:.2f} minutes...")
                    await asyncio.sleep(sleep_time)
                # Reset window
                window_start_time = time.time()
                queries_run_in_window = 0

            # Generate search parameters from the query
            flight_search_params = generate_flight_search_params_from_query(query, include_additional_criteria)
            if not flight_search_params:
                print(f"Skipping query {i} due to past date")
                continue
            query_uuid = str(uuid.uuid4())
            query["search_uuid"] = query_uuid

            print(f"Processing query {i} with search_uuid {query_uuid}: {query.get('first_human_message')}")

            try:
                flight_helper = SerpFlightSearchHelper(TEST_USER, ChatThread(query.get("thread_id", i), "test"), None)

                _, serp_flight_options = await flight_helper.search_flights_serp(
                    flight_search_params, dummy_schedule_send_message_fn, enable_retry=False
                )

                _, _, new_flight_response_dict = SerpFlightSearchHelper.process_flight_search_results(
                    serp_flight_options, False, None
                )

                file_name = f"search-results-{query_uuid}"
                s3_csv_uri = await s3.save_csv_to_s3(
                    root_folder=ROOT_FOLDER,
                    file_name=f"{file_name}-serp",
                    csv_data=new_flight_response_dict,
                    bucket_name=BUCKET_NAME,
                    extra_path="serp",
                    queue_for_batch=False,
                )

                s3_json_uri = await s3.log_trip_artifact_to_s3(
                    root_folder=ROOT_FOLDER,
                    file_name=f"{file_name}-serp",
                    content=new_flight_response_dict,
                    bucket_name=BUCKET_NAME,
                    extra_path="serp",
                    queue_for_batch=False,
                )

                query["results_s3_path_serp"] = [s3_csv_uri, s3_json_uri]
                print(f"Flight results saved to S3: {query.get('results_s3_path_serp')}")

                spotnana_raw_data = await FlightSearchTools.search_flights_spotnana(
                    flight_search_params, None, dummy_schedule_send_message_fn
                )
                bamlHelper = FlightBamlHelper(TEST_USER, ChatThread(query.get("thread_id", i), "test"), None, None)
                _, _, new_flight_response_dict = bamlHelper.process_flight_response(spotnana_raw_data)

                s3_csv_uri = await s3.save_csv_to_s3(
                    root_folder=ROOT_FOLDER,
                    file_name=f"{file_name}-spotnana",
                    csv_data=new_flight_response_dict,
                    bucket_name=BUCKET_NAME,
                    extra_path="spotnana",
                    queue_for_batch=False,
                )

                s3_json_uri = await s3.log_trip_artifact_to_s3(
                    root_folder=ROOT_FOLDER,
                    file_name=f"{file_name}-spotnana",
                    content=new_flight_response_dict,
                    bucket_name=BUCKET_NAME,
                    extra_path="spotnana",
                    queue_for_batch=False,
                )

                query["results_s3_path_spotnana"] = [s3_csv_uri, s3_json_uri]
                print(f"Flight results saved to S3: {query.get('results_s3_path_spotnana')}")
                queries_to_save.append(query)
            except Exception as e:
                print(f"Error processing query {i}: {e}")
                query["error"] = str(e)
                queries_to_save.append(query)
            queries_run_in_window += 1
            await sleep_with_countdown(5)

    except Exception as e:
        print(f"Got exception: {e}")
    finally:
        datetime_str = datetime.now().strftime("%Y%m%d%H%M%S")
        s3_uri = await s3.log_trip_artifact_to_s3(
            root_folder=ROOT_FOLDER,
            file_name=f"search-queries-{datetime_str}",
            content=queries_to_save,
            bucket_name=BUCKET_NAME,
            extra_path="queries",
            queue_for_batch=False,
        )
        print(f"Queries saved to S3: {s3_uri}")
        await GlobalTaskManager.shutdown()


async def sleep_with_countdown(total_seconds):
    for remaining in range(int(total_seconds), 0, -1):
        mins, secs = divmod(remaining, 60)
        print(f"\rSleeping... {mins:02}:{secs:02} remaining", end="")
        await asyncio.sleep(1)
    print("\rSleeping... 00:00 remaining. Waking up now!        ")


async def main(input_file: str, start_index: int = 0, end_index: Optional[int] = None):
    await process_flight_queries(input_file=input_file, start_index=start_index, end_index=end_index)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process flight queries.")
    parser.add_argument("-i", "--input", required=True, help="Input file name for queries (with or without .json)")
    parser.add_argument(
        "-s",
        "--start",
        type=int,
        default=0,
        help="Index to start processing from. If not provided, start from the index 0.",
    )
    parser.add_argument("-e", "--end", type=int, default=None, help="Index to stop processing at (exclusive)")
    args = parser.parse_args()
    asyncio.run(main(input_file=args.input, start_index=args.start, end_index=args.end))
