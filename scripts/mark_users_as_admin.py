#!/usr/bin/env python3
import asyncio
import logging
from typing import Set

from server.database.models.user import User<PERSON><PERSON>, get_all_users
from server.utils.pg_connector import async_session
from server.utils.settings import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Set of email addresses to update
EMAILS_TO_ADMIN: Set[str] = {
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
}


async def mark_as_admin() -> None:
    """
    Updates the role of specified users to admin.
    Handles multiple users with the same email and skips emails that aren't found.
    """
    try:
        async with async_session() as session:
            async with session.begin():
                # Get all users first
                all_users = await get_all_users()

                # Track which emails we've found and processed
                found_emails = set()

                # Process all users
                for user in all_users:
                    if user.email in EMAILS_TO_ADMIN:
                        user.role = UserRole.admin
                        session.add(user)
                        logging.info(f"Updated role to admin for user {user.email} (ID: {user.id})")
                        found_emails.add(user.email)

                # Log any emails that weren't found
                missing_emails = EMAILS_TO_ADMIN - found_emails
                for email in missing_emails:
                    logging.info(f"Skipping: no users found with email {email}")

                # Changes will be committed when the session context exits
                logging.info(f"Completed processing all users for {settings.OTTO_ENV} environment.")
    except Exception as e:
        logging.error(f"Error updating user roles: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(mark_as_admin())
